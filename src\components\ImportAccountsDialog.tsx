import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "./ui/dialog";
import { Button } from "./ui/button";
import { Textarea } from "./ui/textarea";
import { Label } from "./ui/label";
import { Upload, Download, Trash2 } from "lucide-react";
import { localAccountsDb } from "../lib/db";
import { toast } from "sonner";

interface ImportAccountsDialogProps {
  onImportComplete?: () => void;
}

export function ImportAccountsDialog({ onImportComplete }: ImportAccountsDialogProps) {
  const [open, setOpen] = useState(false);
  const [jsonData, setJsonData] = useState("");
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isClearing, setIsClearing] = useState(false);

  const handleImport = async () => {
    if (!jsonData.trim()) {
      toast.error("Please enter JSON data to import");
      return;
    }

    setIsImporting(true);
    try {
      await localAccountsDb.importAccounts(jsonData);
      toast.success("Accounts imported successfully!");
      setJsonData("");
      setOpen(false);
      onImportComplete?.();
    } catch (error) {
      console.error("Import error:", error);
      toast.error("Failed to import accounts. Please check your JSON format.");
    } finally {
      setIsImporting(false);
    }
  };

  const handleExport = async () => {
    setIsExporting(true);
    try {
      const exportData = await localAccountsDb.exportAccounts();
      setJsonData(exportData);
      toast.success("Accounts exported to text area!");
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Failed to export accounts");
    } finally {
      setIsExporting(false);
    }
  };

  const handleClearAll = async () => {
    if (!confirm("Are you sure you want to delete ALL accounts? This cannot be undone!")) {
      return;
    }

    setIsClearing(true);
    try {
      await localAccountsDb.clearAllAccounts();
      toast.success("All accounts cleared!");
      onImportComplete?.();
    } catch (error) {
      console.error("Clear error:", error);
      toast.error("Failed to clear accounts");
    } finally {
      setIsClearing(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setJsonData(content);
      };
      reader.readAsText(file);
    }
  };

  const downloadAsFile = () => {
    if (!jsonData.trim()) {
      toast.error("No data to download");
      return;
    }

    const blob = new Blob([jsonData], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `accounts-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success("File downloaded!");
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Upload className="h-4 w-4 mr-2" />
          Import/Export
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Import/Export Accounts</DialogTitle>
          <DialogDescription>
            Import accounts from JSON backup or export current accounts.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="flex gap-2 flex-wrap">
            <Button
              onClick={handleExport}
              disabled={isExporting}
              variant="outline"
              size="sm"
            >
              <Download className="h-4 w-4 mr-2" />
              {isExporting ? "Exporting..." : "Export Current"}
            </Button>
            
            <Button
              onClick={downloadAsFile}
              disabled={!jsonData.trim()}
              variant="outline"
              size="sm"
            >
              <Download className="h-4 w-4 mr-2" />
              Download File
            </Button>
            
            <Button
              onClick={handleClearAll}
              disabled={isClearing}
              variant="destructive"
              size="sm"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {isClearing ? "Clearing..." : "Clear All"}
            </Button>
          </div>
          
          <div>
            <Label htmlFor="file-upload">Upload JSON File</Label>
            <input
              id="file-upload"
              type="file"
              accept=".json"
              onChange={handleFileUpload}
              className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
          </div>
          
          <div>
            <Label htmlFor="json-data">JSON Data</Label>
            <Textarea
              id="json-data"
              placeholder="Paste your JSON backup here or use Export Current to see current data..."
              value={jsonData}
              onChange={(e) => setJsonData(e.target.value)}
              className="min-h-[300px] font-mono text-sm"
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleImport}
            disabled={isImporting || !jsonData.trim()}
          >
            {isImporting ? "Importing..." : "Import Accounts"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}