import React, { useState } from "react";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import { Label } from "./ui/label";
import { localAccountsDb, LocalAccount } from "@/lib/db";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { Card, CardContent } from "./ui/card";

interface LocalAccountFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  initialData?: Partial<LocalAccount>;
}

export function LocalAccountForm({
  onSuccess,
  onCancel,
  initialData,
}: LocalAccountFormProps) {
  const [formData, setFormData] = useState<Partial<LocalAccount>>({
    id: initialData?.id,
    email: initialData?.email || "",
    password: initialData?.password || "",
    notes: initialData?.notes || "",
    offerId: initialData?.offerId || "",
    status: initialData?.status || "none",
    accountType: initialData?.accountType || "player-auctions",
    price: initialData?.price,
    soldDate: initialData?.soldDate,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleStatusChange = (value: string) => {
    setFormData((prev) => {
      const newState: Partial<LocalAccount> = {
        ...prev,
        status: value as "sold" | "listed" | "ready" | "none",
      };

      // Set date fields based on status
      if (value === "sold" && !prev.soldDate) {
        newState.soldDate = new Date();
      } else if (value === "listed" && !prev.listedDate) {
        newState.listedDate = new Date();
      }

      return newState;
    });
  };

  const handleAccountTypeChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      accountType: value as "eldorado" | "player-auctions",
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Trim email and password
    const trimmedEmail = formData.email?.trim();
    const trimmedPassword = formData.password?.trim();
    
    if (!trimmedEmail || !trimmedPassword) return;

    setIsSubmitting(true);
    try {
      // Check for existing account with same email
      const accounts = await localAccountsDb.getAccounts();
      const existingAccount = accounts.find(
        account => account.email.toLowerCase() === trimmedEmail.toLowerCase() && 
        (!formData.id || account.id !== formData.id)
      );

      if (existingAccount) {
        alert(`An account with email ${trimmedEmail} already exists!`);
        setIsSubmitting(false);
        return;
      }

      // If we have an ID, we're editing an existing account
      if (formData.id) {
        await localAccountsDb.updateAccount({
          ...formData,
          email: trimmedEmail,
          password: trimmedPassword
        } as LocalAccount);
      } else {
        // Otherwise, we're adding a new account
        await localAccountsDb.addAccount({
          email: trimmedEmail,
          password: trimmedPassword,
          notes: formData.notes || "",
          status: "none", // Default to "none" status
          accountType: formData.accountType || "player-auctions",
        });
      }
      onSuccess?.();
    } catch (error) {
      console.error("Error saving account:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="bg-card border-0 shadow-none">
      <CardContent className="p-0">
        <form onSubmit={handleSubmit} autoComplete="off" className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium">
              Email
            </Label>
            <Input
              id="email"
              name="email"
              type="text"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={handleChange}
              autoComplete="off"
              className="bg-background"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm font-medium">
              Password
            </Label>
            <Input
              id="password"
              name="password"
              type="text"
              placeholder="Enter password"
              value={formData.password}
              onChange={handleChange}
              required
              autoComplete="off"
              className="bg-background font-mono"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="price" className="text-sm font-medium">
              Price (USD)
            </Label>
            <Input
              id="price"
              name="price"
              type="number"
              value={formData.price || ""}
              onChange={(e) => {
                const value = e.target.value
                  ? parseFloat(e.target.value)
                  : undefined;
                setFormData((prev) => ({ ...prev, price: value }));
              }}
              placeholder="Enter price in USD"
              step="0.01"
              className="bg-background"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="accountType" className="text-sm font-medium">
              Account Type
            </Label>
            <Select
              value={formData.accountType || "player-auctions"}
              onValueChange={handleAccountTypeChange}
            >
              <SelectTrigger className="bg-background">
                <SelectValue placeholder="Select account type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="player-auctions">Player Auctions</SelectItem>
                <SelectItem value="eldorado">Eldorado</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status" className="text-sm font-medium">
              Status
            </Label>
            <Select
              value={formData.status || "none"}
              onValueChange={handleStatusChange}
            >
              <SelectTrigger className="bg-background">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                <SelectItem value="ready">Ready</SelectItem>
                <SelectItem value="listed">Listed</SelectItem>
                <SelectItem value="sold">Sold</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes" className="text-sm font-medium">
              Notes (optional)
            </Label>
            <Textarea
              id="notes"
              name="notes"
              placeholder="Additional information about this account"
              value={formData.notes}
              onChange={handleChange}
              rows={3}
              className="bg-background resize-none min-h-[80px]"
            />
          </div>

          {formData.status === "sold" && (
            <div className="space-y-2">
              <Label htmlFor="soldDate" className="text-sm font-medium">
                Sold Date
              </Label>
              <Input
                id="soldDate"
                name="soldDate"
                type="date"
                value={formData.soldDate ? new Date(formData.soldDate).toISOString().split('T')[0] : ""}
                onChange={(e) => {
                  const value = e.target.value ? new Date(e.target.value) : undefined;
                  setFormData((prev) => ({ ...prev, soldDate: value }));
                }}
                className="bg-background"
              />
            </div>
          )}

          <div className="flex justify-end gap-2 pt-2">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="border-input"
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting} className="gap-1">
              {isSubmitting
                ? "Saving..."
                : formData.id
                ? "Update Account"
                : "Save Account"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
