// File System Access API utilities
export interface FileSystemAccessOptions {
  accept?: Record<string, string[]>;
  multiple?: boolean;
}

// File system entry interface for directory browsing
export interface FileSystemEntry {
  name: string;
  kind: 'file' | 'directory';
  handle: FileSystemFileHandle | FileSystemDirectoryHandle;
  size?: number;
  lastModified?: Date;
  isImage?: boolean;
}

// Navigation state interface
export interface NavigationState {
  currentPath: string[];
  currentHandle: FileSystemDirectoryHandle;
  parentHandle: FileSystemDirectoryHandle | null;
  canGoUp: boolean;
  canGoBack: boolean;
}

// File selection state interface
export interface SelectionState {
  selectedHandles: Set<FileSystemFileHandle>;
  selectedCount: number;
  totalSize: number;
  hasValidSelection: boolean;
}

export const isFileSystemAccessSupported = (): boolean => {
  return 'showOpenFilePicker' in window && 'showDirectoryPicker' in window;
};

// Storage keys for IndexedDB
const ACCOUNTS_DIR_KEY = 'accounts_directory_handle';

// Store and retrieve directory handle using IndexedDB
const storeDirectoryHandle = async (handle: FileSystemDirectoryHandle): Promise<void> => {
  try {
    const db = await openDB();
    const transaction = db.transaction(['handles'], 'readwrite');
    const store = transaction.objectStore('handles');
    await store.put(handle, ACCOUNTS_DIR_KEY);
  } catch (error) {
    console.warn('Could not store directory handle:', error);
  }
};

const removeStoredDirectoryHandle = async (): Promise<void> => {
  try {
    const db = await openDB();
    const transaction = db.transaction(['handles'], 'readwrite');
    const store = transaction.objectStore('handles');
    await store.delete(ACCOUNTS_DIR_KEY);
  } catch (error) {
    console.warn('Could not remove directory handle:', error);
  }
};

export const getStoredDirectoryHandle = async (): Promise<FileSystemDirectoryHandle | null> => {
  try {
    const db = await openDB();
    const transaction = db.transaction(['handles'], 'readonly');
    const store = transaction.objectStore('handles');
    const request = store.get(ACCOUNTS_DIR_KEY);
    const handle = await new Promise<FileSystemDirectoryHandle | undefined>((resolve, reject) => {
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });

    // Verify the handle is still valid and has permissions
    if (handle) {
      const permission = await (handle as any).queryPermission({ mode: 'read' });
      if (permission === 'granted') {
        return handle;
      } else if (permission === 'prompt') {
        // Try to request permission again
        const newPermission = await (handle as any).requestPermission({ mode: 'read' });
        if (newPermission === 'granted') {
          return handle;
        }
      }
      // If permission is denied or request failed, remove the invalid handle
      console.warn('Directory handle permission denied, removing stored handle');
      await removeStoredDirectoryHandle();
    }
  } catch (error) {
    console.warn('Could not retrieve or verify directory handle:', error);
    // Remove invalid handle
    await removeStoredDirectoryHandle();
  }
  return null;
};

const openDB = (): Promise<IDBDatabase> => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('FileSystemHandles', 1);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);

    request.onupgradeneeded = () => {
      const db = request.result;
      if (!db.objectStoreNames.contains('handles')) {
        db.createObjectStore('handles');
      }
    };
  });
};

export const setupAccountsDirectory = async (): Promise<boolean> => {
  if (!isFileSystemAccessSupported()) {
    throw new Error('File System Access API is not supported');
  }

  try {
    const dirHandle = await (window as any).showDirectoryPicker({
      mode: 'read',
      startIn: 'desktop' // Start in a user-friendly location
    });

    // Verify we can access the directory
    const permission = await (dirHandle as any).queryPermission({ mode: 'read' });
    if (permission !== 'granted') {
      const requestedPermission = await (dirHandle as any).requestPermission({ mode: 'read' });
      if (requestedPermission !== 'granted') {
        throw new Error('Directory access permission denied');
      }
    }

    await storeDirectoryHandle(dirHandle);
    return true;
  } catch (error) {
    if ((error as Error).name === 'AbortError') {
      return false; // User cancelled
    }
    throw error;
  }
};

export const openFileFromAccountsDirectory = async (
  options: FileSystemAccessOptions = {}
): Promise<File[]> => {
  if (!isFileSystemAccessSupported()) {
    throw new Error('File System Access API is not supported');
  }

  try {
    // Try to get the stored accounts directory
    const accountsDir = await getStoredDirectoryHandle();

    const pickerOptions: any = {
      multiple: options.multiple || false,
      excludeAcceptAllOption: true,
    };

    if (options.accept) {
      pickerOptions.types = [
        {
          description: 'Images',
          accept: options.accept,
        },
      ];
    }

    // Use the accounts directory if available
    if (accountsDir) {
      pickerOptions.startIn = accountsDir;
    }

    const fileHandles = await (window as any).showOpenFilePicker(pickerOptions);
    const files: File[] = [];

    for (const fileHandle of fileHandles) {
      const file = await fileHandle.getFile();
      files.push(file);
    }

    return files;
  } catch (error) {
    if ((error as Error).name === 'AbortError') {
      return [];
    }
    throw error;
  }
};

export const hasAccountsDirectorySetup = async (): Promise<boolean> => {
  const handle = await getStoredDirectoryHandle();
  return handle !== null;
};

export const openDirectoryWithSystemAccess = async (): Promise<FileSystemDirectoryHandle | null> => {
  if (!('showDirectoryPicker' in window)) {
    throw new Error('Directory picker is not supported in this browser');
  }

  try {
    return await (window as any).showDirectoryPicker();
  } catch (error) {
    if ((error as Error).name === 'AbortError') {
      return null;
    }
    throw error;
  }
};

// Directory browsing and navigation utilities

/**
 * Read directory contents and return as FileSystemEntry array
 */
export const readDirectoryContents = async (
  directoryHandle: FileSystemDirectoryHandle
): Promise<FileSystemEntry[]> => {
  const entries: FileSystemEntry[] = [];

  try {
    // Use type assertion for File System Access API
    for await (const [name, handle] of (directoryHandle as any).entries()) {
      const entry: FileSystemEntry = {
        name,
        kind: handle.kind,
        handle,
      };

      // Add additional metadata for files
      if (handle.kind === 'file') {
        try {
          const file = await (handle as FileSystemFileHandle).getFile();
          entry.size = file.size;
          entry.lastModified = new Date(file.lastModified);
          entry.isImage = isImageFile(file.type);
        } catch (error) {
          console.warn(`Could not read file metadata for ${name}:`, error);
        }
      }

      entries.push(entry);
    }

    // Sort entries: directories first, then files, both alphabetically
    return entries.sort((a, b) => {
      if (a.kind !== b.kind) {
        return a.kind === 'directory' ? -1 : 1;
      }
      return a.name.localeCompare(b.name, undefined, { numeric: true });
    });
  } catch (error) {
    throw new Error(`Failed to read directory contents: ${error}`);
  }
};

/**
 * Navigate to a specific directory by path
 */
export const navigateToDirectory = async (
  rootHandle: FileSystemDirectoryHandle,
  pathSegments: string[]
): Promise<FileSystemDirectoryHandle> => {
  let currentHandle = rootHandle;

  for (const segment of pathSegments) {
    if (!segment) continue;

    try {
      const handle = await currentHandle.getDirectoryHandle(segment);
      currentHandle = handle;
    } catch (error) {
      throw new Error(`Directory not found: ${pathSegments.join('/')}`);
    }
  }

  return currentHandle;
};

/**
 * Navigate to default accounts directory
 */
export const navigateToDefaultDirectory = async (): Promise<FileSystemDirectoryHandle | null> => {
  try {
    // First try to get stored directory handle
    const storedHandle = await getStoredDirectoryHandle();
    if (storedHandle) {
      return storedHandle;
    }

    // If no stored handle, request directory picker
    const rootHandle = await openDirectoryWithSystemAccess();
    if (!rootHandle) {
      return null;
    }

    // Try to navigate to the default path: accounts
    try {
      const accountsHandle = await rootHandle.getDirectoryHandle('accounts');
      await storeDirectoryHandle(accountsHandle);
      return accountsHandle;
    } catch (error) {
      // If accounts directory doesn't exist, use root directory
      await storeDirectoryHandle(rootHandle);
      return rootHandle;
    }
  } catch (error) {
    console.warn('Could not navigate to default directory:', error);
    return null;
  }
};

/**
 * Get parent directory handle if possible
 */
export const getParentDirectory = async (
  currentHandle: FileSystemDirectoryHandle,
  rootHandle: FileSystemDirectoryHandle
): Promise<FileSystemDirectoryHandle | null> => {
  // Note: File System Access API doesn't provide direct parent access
  // This is a limitation we'll need to work around by maintaining navigation history
  if (await isSameDirectory(currentHandle, rootHandle)) {
    return null; // Already at root
  }

  // For now, return null - parent navigation will be handled via navigation history
  return null;
};

/**
 * Check if two directory handles refer to the same directory
 */
export const isSameDirectory = async (
  handle1: FileSystemDirectoryHandle,
  handle2: FileSystemDirectoryHandle
): Promise<boolean> => {
  try {
    return await handle1.isSameEntry(handle2);
  } catch (error) {
    return false;
  }
};

/**
 * Convert FileSystemFileHandle to File object
 */
export const convertHandleToFile = async (
  fileHandle: FileSystemFileHandle
): Promise<File> => {
  try {
    return await fileHandle.getFile();
  } catch (error) {
    throw new Error(`Failed to convert file handle to File: ${error}`);
  }
};

/**
 * Convert multiple FileSystemFileHandles to File objects
 */
export const convertHandlesToFiles = async (
  fileHandles: FileSystemFileHandle[]
): Promise<File[]> => {
  const files: File[] = [];

  for (const handle of fileHandles) {
    try {
      const file = await convertHandleToFile(handle);
      files.push(file);
    } catch (error) {
      console.warn(`Failed to convert file handle ${handle.name}:`, error);
    }
  }

  return files;
};

/**
 * Validate file type against accept criteria
 */
export const validateFileType = (
  file: File,
  accept?: Record<string, string[]>
): boolean => {
  if (!accept) return true;

  for (const [mimeType, extensions] of Object.entries(accept)) {
    // Check MIME type
    if (file.type === mimeType) return true;

    // Check file extension
    const fileName = file.name.toLowerCase();
    for (const ext of extensions) {
      if (fileName.endsWith(ext.toLowerCase())) return true;
    }
  }

  return false;
};

/**
 * Check if file is an image based on MIME type
 */
export const isImageFile = (mimeType: string): boolean => {
  return mimeType.startsWith('image/');
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
};

/**
 * Format date for display
 */
export const formatFileDate = (date: Date): string => {
  return date.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};