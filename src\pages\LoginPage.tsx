import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Key, LogIn } from "lucide-react";
import { authService } from "@/lib/auth";

export default function LoginPage() {
  const [token, setToken] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Parse URL query parameters on mount
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const urlToken = queryParams.get("token");

    if (urlToken) {
      setToken(urlToken);
      // Auto-continue if token is provided via URL
      handleLogin(urlToken);
    }
  }, [location, navigate]);

  const handleLogin = async (tokenToUse = token) => {
    if (!tokenToUse.trim()) {
      return; // Don't proceed if token is empty
    }

    setIsLoading(true);

    try {
      // Store token using auth service
      authService.setToken(tokenToUse);

      // Simulate API validation (replace with actual validation if needed)
      await new Promise((resolve) => setTimeout(resolve, 800));

      // Check if we have a redirect URL
      const queryParams = new URLSearchParams(location.search);
      const redirectTo = queryParams.get("redirectTo") || "/";

      // Redirect after successful login
      navigate(redirectTo);
    } catch (error) {
      console.error("Login failed:", error);
      // You could add error handling/messaging here
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <div className="w-full max-w-md">
        <Card className="border-border/40 shadow-lg">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-bold">Authentication</CardTitle>
            <CardDescription className="text-muted-foreground">
              Enter your authentication token to continue
            </CardDescription>
          </CardHeader>

          <CardContent>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                <Key className="h-4 w-4" />
              </div>
              <Input
                type="text"
                placeholder="Paste your authentication token here"
                value={token}
                onChange={(e) => setToken(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>

          <CardFooter>
            <Button
              className="w-full"
              onClick={() => handleLogin()}
              disabled={isLoading || !token.trim()}
            >
              {isLoading ? (
                <span className="flex items-center gap-1">
                  <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  Processing...
                </span>
              ) : (
                <span className="flex items-center gap-2">
                  <LogIn className="h-4 w-4" />
                  Continue
                </span>
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
