import React, { useState, useEffect } from "react";
import { localAccountsDb, LocalAccount } from "@/lib/db";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Users } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";

interface SelectLocalAccountDialogProps {
  onAccountSelected: (account: LocalAccount) => void;
  trigger?: React.ReactNode;
  accountTypeFilter?: "player-auctions" | "eldorado" | null;
}

export function SelectLocalAccountDialog({
  onAccountSelected,
  trigger,
  accountTypeFilter = null,
}: SelectLocalAccountDialogProps) {
  const [accounts, setAccounts] = useState<LocalAccount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [open, setOpen] = useState(false);

  const loadAccounts = async () => {
    setIsLoading(true);
    try {
      const allAccounts = await localAccountsDb.getAccounts();
      // Filter accounts that don't have an offer ID set
      let availableAccounts = allAccounts.filter(
        (account) => account.status === "ready"
      );
      
      // Apply account type filter if specified
      if (accountTypeFilter) {
        availableAccounts = availableAccounts.filter(
          (account) => account.accountType === accountTypeFilter
        );
      }
      
      setAccounts(availableAccounts);
    } catch (error) {
      console.error("Error loading accounts:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      loadAccounts();
    }
  }, [open, accountTypeFilter]);

  const handleSelectAccount = (account: LocalAccount) => {
    onAccountSelected(account);
    setOpen(false);
  };

  const getStatusBadge = (status: string | undefined) => {
    switch (status) {
      case "ready":
        return <Badge variant="default">Ready</Badge>;
      case "none":
        return <Badge variant="outline">None</Badge>;
      default:
        return <Badge variant="outline">None</Badge>;
    }
  };

  const getAccountTypeBadge = (accountType: string | undefined) => {
    switch (accountType) {
      case "eldorado":
        return <Badge variant="default" className="bg-orange-500 hover:bg-orange-600">Eldorado</Badge>;
      case "player-auctions":
        return <Badge variant="default" className="bg-blue-500 hover:bg-blue-600">Player Auctions</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getDialogTitle = () => {
    if (accountTypeFilter === "player-auctions") {
      return "Select Player Auctions Account";
    } else if (accountTypeFilter === "eldorado") {
      return "Select Eldorado Account";
    } else {
      return "Select Account";
    }
  };

  const getDialogDescription = () => {
    if (accountTypeFilter === "player-auctions") {
      return "Choose from your available Player Auctions accounts";
    } else if (accountTypeFilter === "eldorado") {
      return "Choose from your available Eldorado accounts";
    } else {
      return "Choose from your available accounts";
    }
  };

  const defaultTrigger = (
    <Button variant="outline" className="gap-1.5">
      <Users className="h-4 w-4" />
      Select Account
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>{getDialogTitle()}</DialogTitle>
          <DialogDescription>
            {getDialogDescription()}
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="py-6 text-center text-muted-foreground">
            Loading accounts...
          </div>
        ) : accounts.length === 0 ? (
          <div className="py-6 text-center text-muted-foreground">
            {accountTypeFilter
              ? `No available ${accountTypeFilter === "player-auctions" ? "Player Auctions" : "Eldorado"} accounts ready for listing`
              : "No available accounts ready for listing"}
          </div>
        ) : (
          <ScrollArea className="h-[550px]">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Email</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Notes</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead className="w-[100px]">Action</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {accounts.map((account) => (
                  <TableRow
                    key={account.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSelectAccount(account)}
                  >
                    <TableCell className="font-medium">
                      {account.email}
                    </TableCell>
                    <TableCell>{getAccountTypeBadge(account.accountType)}</TableCell>
                    <TableCell className="truncate max-w-[200px]">
                      {account.notes || "-"}
                    </TableCell>
                    <TableCell>{getStatusBadge(account.status)}</TableCell>
                    <TableCell>
                      {account.price ? `$${account.price.toFixed(2)}` : "-"}
                    </TableCell>
                    <TableCell>
                      <Button
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSelectAccount(account);
                        }}
                        className="w-full"
                      >
                        Select
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
        )}
      </DialogContent>
    </Dialog>
  );
}
