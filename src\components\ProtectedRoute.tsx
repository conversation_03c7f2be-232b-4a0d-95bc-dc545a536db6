import { ReactNode, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { authService } from "@/lib/auth";

interface ProtectedRouteProps {
  children: ReactNode;
}

/**
 * A component that protects routes by requiring authentication.
 * If user is not authenticated, they will be redirected to the login page.
 */
export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Check if user is authenticated
    if (!authService.isAuthenticated()) {
      // Redirect to login page with original destination in query params for later redirect
      navigate(`/login?redirectTo=${encodeURIComponent(location.pathname)}`, {
        replace: true,
      });
    }
  }, [navigate, location]);

  // If authenticated, render the children
  if (authService.isAuthenticated()) {
    return <>{children}</>;
  }

  // Return null while redirecting
  return null;
}
