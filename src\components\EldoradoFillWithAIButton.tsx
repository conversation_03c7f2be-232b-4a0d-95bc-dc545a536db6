import React, { useState, useRef, useCallback } from "react";
import { useAtom } from "jotai";
import { Button } from "@/components/ui/button";
import { Sparkles, FileText, X, Settings } from "lucide-react";
import { openRouterService } from "@/lib/openrouter-api";
import { eldoradoTitlePrompt, eldoradoDescriptionPrompt } from "@/lib/ai-prompts";
import { aiSettingsAtom } from "@/lib/atoms";
import { AISettingsDialog } from "@/components/AISettingsDialog";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

interface EldoradoFillWithAIButtonProps {
  inputText: string;
  onTitleGenerated: (title: string) => void;
  onDescriptionGenerated: (description: string) => void;
}

export function EldoradoFillWithAIButton({
  inputText,
  onTitleGenerated,
  onDescriptionGenerated,
}: EldoradoFillWithAIButtonProps) {
  const [aiSettings] = useAtom(aiSettingsAtom);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [additionalFile, setAdditionalFile] = useState<File | null>(null);
  const [additionalText, setAdditionalText] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const processFile = async (file: File) => {
    // Check if it's a text file
    if (!file.type.includes('text') && !file.name.endsWith('.txt')) {
      toast.error("Please select a text file");
      return;
    }

    setAdditionalFile(file);

    // Read the file content
    try {
      const text = await file.text();
      setAdditionalText(text);
      toast.success(`File "${file.name}" loaded successfully`);
    } catch (error) {
      console.error("Error reading file:", error);
      toast.error("Failed to read file content");
      setAdditionalFile(null);
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    await processFile(file);

    // Reset the input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      processFile(file);
    }
  }, []);

  const clearFile = () => {
    setAdditionalFile(null);
    setAdditionalText(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleGenerateContent = async () => {
    if (!inputText.trim()) {
      toast.error("Please enter some text in the title field first");
      return;
    }

    if (!aiSettings.apiKey) {
      toast.error("OpenRouter API key is not configured. Please check AI settings.");
      return;
    }

    setIsGenerating(true);
    toast.info("Generating AI content...");

    try {
      // Generate title using eldorado-specific prompt
      const titleResponse = await openRouterService.generateText({
        prompt: eldoradoTitlePrompt,
        input: inputText,
        apiKey: aiSettings.apiKey,
        additionalInfo: additionalText,
        model: aiSettings.titleModel,
      });

      // Parse title JSON response
      let title = "";
      try {
        const titleJson = JSON.parse(titleResponse);
        title = titleJson.title || titleResponse;
      } catch (parseError) {
        console.warn("Failed to parse title JSON, using raw response:", parseError);
        title = titleResponse;
      }

      // Generate description using eldorado-specific prompt
      const descriptionResponse = await openRouterService.generateText({
        prompt: eldoradoDescriptionPrompt,
        input: inputText,
        apiKey: aiSettings.apiKey,
        additionalInfo: additionalText,
        model: aiSettings.descriptionModel,
      });

      // Parse description JSON response
      let description = "";
      try {
        const descriptionJson = JSON.parse(descriptionResponse);
        description = descriptionJson.description || descriptionResponse;
      } catch (parseError) {
        console.warn("Failed to parse description JSON, using raw response:", parseError);
        description = descriptionResponse;
      }

      // Update form with generated content
      onTitleGenerated(title);
      onDescriptionGenerated(description);

      toast.success("AI content generated successfully");
    } catch (error) {
      console.error("Error generating AI content:", error);
      toast.error("Failed to generate AI content. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept=".txt,text/plain"
        className="hidden"
        id="eldorado-additional-info-file"
      />

      {!additionalFile ? (
        <div
          className={cn(
            "flex items-center justify-center h-9 px-3 border border-dashed rounded-md text-xs cursor-pointer transition-colors",
            isDragging
              ? "border-primary bg-primary/5"
              : "border-muted-foreground/20 hover:border-muted-foreground/50"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <span>Drop text file</span>
        </div>
      ) : (
        <div className="flex items-center gap-1 h-9 px-3 border border-muted-foreground/20 rounded-md text-xs">
          <FileText className="h-3 w-3 text-primary" />
          <span className="truncate max-w-[100px]">{additionalFile.name}</span>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={clearFile}
            className="h-5 w-5 p-0 ml-1"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      )}

      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={handleGenerateContent}
        disabled={isGenerating}
        className="gap-1"
      >
        <Sparkles className="h-4 w-4" />
        {isGenerating ? "Generating..." : "Fill with AI"}
      </Button>

      <AISettingsDialog>
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="gap-1"
        >
          <Settings className="h-4 w-4" />
        </Button>
      </AISettingsDialog>
    </div>
  );
}