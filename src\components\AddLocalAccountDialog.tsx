import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { LocalAccountForm } from "@/components/LocalAccountForm";

interface AddLocalAccountDialogProps {
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onAccountAdded?: () => void;
}

export function AddLocalAccountDialog({
  trigger,
  open,
  onOpenChange,
  onAccountAdded,
}: AddLocalAccountDialogProps) {
  const [dialogOpen, setDialogOpen] = React.useState(open || false);

  const handleOpenChange = (open: boolean) => {
    setDialogOpen(open);
    onOpenChange?.(open);
  };

  const handleSuccess = () => {
    handleOpenChange(false);
    onAccountAdded?.();
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm" className="gap-1 hover:bg-accent">
      <Plus className="h-4 w-4" />
      <span>Add Local Account</span>
    </Button>
  );

  return (
    <Dialog open={dialogOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px] border-border bg-background">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-lg font-semibold">
            Add Local Account
          </DialogTitle>
          <DialogDescription className="text-muted-foreground text-sm">
            Enter your account details below to add it to your local storage.
          </DialogDescription>
        </DialogHeader>
        <LocalAccountForm
          onSuccess={handleSuccess}
          onCancel={() => handleOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
