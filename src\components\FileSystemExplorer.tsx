import { useState, useEffect, useCallback } from "react";
import {
  Folder,
  File,
  ArrowLeft,
  ArrowUp,
  Home,
  Image,
  Check,
  X,
  Grid3X3,
  List
} from "lucide-react";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { ScrollArea } from "./ui/scroll-area";
import { cn } from "../lib/utils";
import {
  FileSystemEntry,
  NavigationState,
  SelectionState,
  readDirectoryContents,
  navigateToDirectory,
  convertHandlesToFiles,
  formatFileSize,
  formatFileDate,
  isImageFile,
  validateFileType
} from "../lib/fileSystemAccess";

interface FileSystemExplorerProps {
  onFilesSelected?: (files: File[]) => void;
  onCancel?: () => void;
  accept?: Record<string, string[]>;
  multiple?: boolean;
  defaultPath?: string;
  autoNavigateToFolder?: string;
  className?: string;
}

export function FileSystemExplorer({
  onFilesSelected,
  onCancel,
  accept = { 'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp', '.svg'] },
  multiple = false,
  defaultPath = "C:\\Users\\<USER>\\Desktop\\GoldfarmData\\accs_sale\\accounts",
  autoNavigateToFolder,
  className
}: FileSystemExplorerProps) {
  const [entries, setEntries] = useState<FileSystemEntry[]>([]);
  const [navigation, setNavigation] = useState<NavigationState | null>(null);
  const [selection, setSelection] = useState<SelectionState>({
    selectedHandles: new Set(),
    selectedCount: 0,
    totalSize: 0,
    hasValidSelection: false
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [navigationHistory, setNavigationHistory] = useState<{
    path: string[];
    handle: FileSystemDirectoryHandle;
  }[]>([]);
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('grid');
  const [imageUrls, setImageUrls] = useState<Map<string, string>>(new Map());

  // Initialize the explorer with default directory
  const initializeExplorer = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Import the function to get stored directory handle
      const { getStoredDirectoryHandle, setupAccountsDirectory } = await import('../lib/fileSystemAccess');
      let rootHandle = await getStoredDirectoryHandle();

      if (!rootHandle) {
        // Try to set up directory access automatically
        try {
          const setupSuccess = await setupAccountsDirectory();
          if (setupSuccess) {
            rootHandle = await getStoredDirectoryHandle();
          }
        } catch (setupError) {
          console.warn('Failed to setup directory automatically:', setupError);
        }

        if (!rootHandle) {
          setError("Directory access required. Please click 'Setup Directory' to select your accounts directory.");
          setIsLoading(false);
          return;
        }
      }

      // Try to navigate to the default path
      let currentHandle = rootHandle;
      const pathSegments: string[] = [];

      try {
        // Parse the default path and navigate to it
        const defaultSegments = defaultPath.split(/[\\\/]/).filter(segment =>
          segment && !segment.includes(':') && segment.trim() !== '' // Skip drive letters and empty segments
        );

        for (const segment of defaultSegments) {
          try {
            const nextHandle = await currentHandle.getDirectoryHandle(segment);
            currentHandle = nextHandle;
            pathSegments.push(segment);
          } catch (error) {
            // If we can't find the exact path, stop here and use current directory
            console.warn(`Could not navigate to ${segment}, stopping at current directory`);
            break;
          }
        }
      } catch (error) {
        console.warn('Could not navigate to default path, using root directory');
      }

      // Set up navigation state
      const navState: NavigationState = {
        currentPath: pathSegments,
        currentHandle,
        parentHandle: null,
        canGoUp: pathSegments.length > 0,
        canGoBack: false
      };

      setNavigation(navState);
      setNavigationHistory([{ path: [...pathSegments], handle: currentHandle }]);

      // Load directory contents
      await loadDirectoryContents(currentHandle);
    } catch (error) {
      if ((error as Error).name === 'AbortError') {
        onCancel?.();
        return;
      }
      setError(`Failed to initialize file explorer: ${(error as Error).message}`);
    } finally {
      setIsLoading(false);
    }
  }, [defaultPath, onCancel]);

  // Create image URLs for direct display
  const createImageUrl = async (entry: FileSystemEntry): Promise<string | null> => {
    if (entry.kind !== 'file' || !entry.isImage) return null;

    try {
      const fileHandle = entry.handle as FileSystemFileHandle;
      const file = await fileHandle.getFile();
      return URL.createObjectURL(file);
    } catch (error) {
      console.warn('Failed to create image URL:', error);
      return null;
    }
  };

  // Auto-navigate to folder that contains the specified string
  const autoNavigateToMatchingFolder = async (directoryHandle: FileSystemDirectoryHandle, searchString: string) => {
    try {
      console.log('🔍 Auto-navigating to folder containing:', searchString);
      const contents = await readDirectoryContents(directoryHandle);
      console.log('📁 Available folders:', contents.filter(e => e.kind === 'directory').map(e => e.name));

      // Find the first directory that contains the search string
      const matchingFolder = contents.find(entry =>
        entry.kind === 'directory' &&
        entry.name.toLowerCase().includes(searchString.toLowerCase())
      );

      console.log('🎯 Matching folder found:', matchingFolder?.name || 'None');

      if (matchingFolder && navigation) {
        console.log('✅ Navigating to:', matchingFolder.name);
        // Navigate to the matching folder
        const newHandle = matchingFolder.handle as FileSystemDirectoryHandle;
        const newPath = [...navigation.currentPath, matchingFolder.name];

        // Add current state to history
        setNavigationHistory(prev => [...prev, {
          path: newPath,
          handle: newHandle
        }]);

        const newNavState: NavigationState = {
          currentPath: newPath,
          currentHandle: newHandle,
          parentHandle: navigation.currentHandle,
          canGoUp: true,
          canGoBack: true
        };

        setNavigation(newNavState);
        await loadDirectoryContents(newHandle);
      } else {
        console.log('❌ No matching folder found or navigation not ready');
      }
    } catch (error) {
      console.warn('Failed to auto-navigate to matching folder:', error);
    }
  };

  // Load directory contents and create image URLs
  const loadDirectoryContents = async (directoryHandle: FileSystemDirectoryHandle) => {
    try {
      setIsLoading(true);
      const contents = await readDirectoryContents(directoryHandle);
      setEntries(contents);

      // View mode is now controlled manually by the user toggle buttons

      // Create image URLs for PNG images
      const newImageUrls = new Map(imageUrls);
      const imagePromises = contents
        .filter(entry => entry.kind === 'file' && entry.isImage && entry.name.toLowerCase().endsWith('.png'))
        .map(async (entry) => {
          const imageUrl = await createImageUrl(entry);
          if (imageUrl) {
            newImageUrls.set(entry.name, imageUrl);
          }
        });

      await Promise.all(imagePromises);
      setImageUrls(newImageUrls);
    } catch (error) {
      setError(`Failed to load directory contents: ${(error as Error).message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Navigate to a subdirectory
  const navigateToSubdirectory = async (entry: FileSystemEntry) => {
    if (entry.kind !== 'directory' || !navigation) return;

    try {
      const newHandle = entry.handle as FileSystemDirectoryHandle;
      const newPath = [...navigation.currentPath, entry.name];

      // Add current state to history
      setNavigationHistory(prev => [...prev, {
        path: newPath,
        handle: newHandle
      }]);

      const newNavState: NavigationState = {
        currentPath: newPath,
        currentHandle: newHandle,
        parentHandle: navigation.currentHandle,
        canGoUp: true,
        canGoBack: true
      };

      setNavigation(newNavState);
      await loadDirectoryContents(newHandle);

      // Clear selection when navigating
      setSelection({
        selectedHandles: new Set(),
        selectedCount: 0,
        totalSize: 0,
        hasValidSelection: false
      });
    } catch (error) {
      setError(`Failed to navigate to directory: ${(error as Error).message}`);
    }
  };

  // Navigate back in history
  const navigateBack = () => {
    if (navigationHistory.length <= 1) return;

    const newHistory = [...navigationHistory];
    newHistory.pop(); // Remove current
    const previous = newHistory[newHistory.length - 1];

    setNavigationHistory(newHistory);
    setNavigation({
      currentPath: previous.path,
      currentHandle: previous.handle,
      parentHandle: null,
      canGoUp: previous.path.length > 0,
      canGoBack: newHistory.length > 1
    });

    loadDirectoryContents(previous.handle);

    // Clear selection
    setSelection({
      selectedHandles: new Set(),
      selectedCount: 0,
      totalSize: 0,
      hasValidSelection: false
    });
  };

  // Navigate up one level
  const navigateUp = () => {
    if (!navigation?.canGoUp || navigationHistory.length <= 1) return;
    navigateBack();
  };

  // Toggle file selection
  const toggleFileSelection = (entry: FileSystemEntry) => {
    if (entry.kind !== 'file') return;

    const fileHandle = entry.handle as FileSystemFileHandle;
    const newSelectedHandles = new Set(selection.selectedHandles);

    if (newSelectedHandles.has(fileHandle)) {
      newSelectedHandles.delete(fileHandle);
    } else {
      if (!multiple) {
        newSelectedHandles.clear();
      }
      newSelectedHandles.add(fileHandle);
    }

    // Calculate total size and validate selection
    let totalSize = 0;
    let hasValidSelection = false;

    for (const handle of newSelectedHandles) {
      const selectedEntry = entries.find(e => e.handle === handle);
      if (selectedEntry?.size) {
        totalSize += selectedEntry.size;
      }
      // Check if at least one file matches accept criteria
      if (selectedEntry && (!accept || entry.isImage)) {
        hasValidSelection = true;
      }
    }

    setSelection({
      selectedHandles: newSelectedHandles,
      selectedCount: newSelectedHandles.size,
      totalSize,
      hasValidSelection
    });
  };

  // Confirm selection
  const confirmSelection = async () => {
    if (!selection.hasValidSelection || selection.selectedHandles.size === 0) return;

    try {
      const files = await convertHandlesToFiles(Array.from(selection.selectedHandles));
      onFilesSelected?.(files);
    } catch (error) {
      setError(`Failed to process selected files: ${(error as Error).message}`);
    }
  };

  // Initialize on mount
  useEffect(() => {
    initializeExplorer();
  }, [initializeExplorer]);

  // Auto-navigate when navigation is ready and autoNavigateToFolder is provided
  useEffect(() => {
    if (navigation && autoNavigateToFolder && navigation.currentPath.length === 0) {
      // Only auto-navigate if we're at the root level (no path segments)
      console.log('🚀 Triggering auto-navigation after navigation state is ready');
      autoNavigateToMatchingFolder(navigation.currentHandle, autoNavigateToFolder);
    }
  }, [navigation, autoNavigateToFolder]);

  if (error) {
    return (
      <Card className={cn("p-6", className)}>
        <div className="text-center">
          <p className="text-destructive mb-4">{error}</p>
          <div className="flex gap-2 justify-center">
            <Button onClick={initializeExplorer}>Setup Directory</Button>
            <Button variant="outline" onClick={onCancel}>Cancel</Button>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={cn("flex flex-col h-[600px]", className)}>
      {/* Header with navigation */}
      <div className="flex items-center gap-2 p-4 border-b">
        <Button
          variant="ghost"
          size="sm"
          onClick={navigateBack}
          disabled={!navigation?.canGoBack}
        >
          <ArrowLeft className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={navigateUp}
          disabled={!navigation?.canGoUp}
        >
          <ArrowUp className="w-4 h-4" />
        </Button>
        <div className="flex-1 flex items-center gap-1 text-sm text-muted-foreground">
          <Home className="w-4 h-4" />
          {navigation?.currentPath.map((segment, index) => (
            <span key={index}>
              / {segment}
            </span>
          )) || '/'}
          {autoNavigateToFolder && (
            <span className="ml-2 px-2 py-1 bg-yellow-500/20 text-yellow-600 rounded text-xs">
              Auto: "{autoNavigateToFolder}"
            </span>
          )}
        </div>

        {/* View toggle buttons */}
        <div className="flex items-center gap-1 border rounded-md p-1">
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('list')}
            className="h-7 w-7 p-0"
          >
            <List className="w-4 h-4" />
          </Button>
          <Button
            variant={viewMode === 'grid' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('grid')}
            className="h-7 w-7 p-0"
          >
            <Grid3X3 className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* File list */}
      <ScrollArea className="flex-1 ">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="w-6 h-6 border-2 border-primary/30 border-t-primary rounded-full animate-spin"></div>
          </div>
        ) : (
          <div className="p-2">
            {viewMode === 'grid' ? (
              /* Grid view for subdirectories */
              <div className="grid  grid-cols-5 ">
                {entries.map((entry, index) => (
                  <div
                    key={`${entry.name}-${index}`}
                    className={cn(
                      "flex flex-col items-center p-2 rounded-md hover:bg-accent cursor-pointer transition-colors",
                      entry.kind === 'file' && selection.selectedHandles.has(entry.handle as FileSystemFileHandle) &&
                      "bg-primary/10 border-2 border-primary/40"
                    )}
                    onClick={() => {
                      if (entry.kind === 'directory') {
                        navigateToSubdirectory(entry);
                      } else {
                        toggleFileSelection(entry);
                      }
                    }}
                  >
                    <div className="relative mb-2">
                      {entry.kind === 'directory' ? (
                        <div className="w-32 h-32 flex items-center justify-center bg-blue-500/10 border border-blue-500/20 rounded-lg">
                          <Folder className="w-12 h-12 text-blue-500" />
                        </div>
                      ) : entry.isImage && imageUrls.has(entry.name) ? (
                        <div className="w-32 h-32 rounded-lg overflow-hidden bg-muted/20  flex items-center justify-center">
                          <img
                            src={imageUrls.get(entry.name)}
                            alt={entry.name}
                            className="max-w-full max-h-full object-contain"
                          />
                        </div>
                      ) : entry.isImage ? (
                        <div className="w-32 h-32 flex items-center justify-center bg-muted/20 border border-green-500/20 rounded-lg">
                          <Image className="w-12 h-12 text-green-500" />
                        </div>
                      ) : (
                        <div className="w-32 h-32 flex items-center justify-center bg-muted/20   rounded-lg">
                          <File className="w-12 h-12 text-muted-foreground" />
                        </div>
                      )}

                      {entry.kind === 'file' && selection.selectedHandles.has(entry.handle as FileSystemFileHandle) && (
                        <div className="absolute -top-1 -right-1 w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                          <Check className="w-3 h-3 text-primary-foreground" />
                        </div>
                      )}
                    </div>

                    <div className="text-xs text-center">
                      <div className="font-medium truncate w-full" title={entry.name}>
                        {entry.name}
                      </div>
                      {entry.kind === 'file' && entry.size && (
                        <div className="text-muted-foreground mt-1">
                          {formatFileSize(entry.size)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              /* List view for root directory */
              <div>
                {entries.map((entry, index) => (
                  <div
                    key={`${entry.name}-${index}`}
                    className={cn(
                      "flex items-center gap-3 p-2 rounded-md hover:bg-accent cursor-pointer",
                      entry.kind === 'file' && selection.selectedHandles.has(entry.handle as FileSystemFileHandle) &&
                      "bg-primary/10 border border-primary/20"
                    )}
                    onClick={() => {
                      if (entry.kind === 'directory') {
                        navigateToSubdirectory(entry);
                      } else {
                        toggleFileSelection(entry);
                      }
                    }}
                  >
                    <div className="flex-shrink-0">
                      {entry.kind === 'directory' ? (
                        <Folder className="w-5 h-5 text-blue-500" />
                      ) : entry.isImage ? (
                        <Image className="w-5 h-5 text-green-500" />
                      ) : (
                        <File className="w-5 h-5 text-gray-500" />
                      )}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{entry.name}</div>
                      {entry.kind === 'file' && (
                        <div className="text-xs text-muted-foreground">
                          {entry.size && formatFileSize(entry.size)}
                          {entry.lastModified && ` • ${formatFileDate(entry.lastModified)}`}
                        </div>
                      )}
                    </div>

                    {entry.kind === 'file' && selection.selectedHandles.has(entry.handle as FileSystemFileHandle) && (
                      <Check className="w-4 h-4 text-primary" />
                    )}
                  </div>
                ))}
              </div>
            )}

            {entries.length === 0 && !isLoading && (
              <div className="text-center py-8 text-muted-foreground">
                This directory is empty
              </div>
            )}
          </div>
        )}
      </ScrollArea>

      {/* Footer with selection info and actions */}
      <div className="flex items-center justify-between p-4 border-t bg-muted/30">
        <div className="text-sm text-muted-foreground">
          {selection.selectedCount > 0 ? (
            <>
              {selection.selectedCount} file{selection.selectedCount !== 1 ? 's' : ''} selected
              {selection.totalSize > 0 && ` (${formatFileSize(selection.totalSize)})`}
            </>
          ) : (
            `${entries.filter(e => e.kind === 'file').length} files, ${entries.filter(e => e.kind === 'directory').length} folders`
          )}
        </div>

        <div className="flex gap-2">
          <Button variant="outline" onClick={onCancel}>
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button
            onClick={confirmSelection}
            disabled={!selection.hasValidSelection}
          >
            <Check className="w-4 h-4 mr-2" />
            Select ({selection.selectedCount})
          </Button>
        </div>
      </div>
    </Card>
  );
}