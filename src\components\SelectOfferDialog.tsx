import React, { useState, useEffect, useMemo } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { offersService } from "@/lib/api";
import { localAccountsDb, LocalAccount } from "@/lib/db";
import { Link } from "lucide-react";
import { useQuery } from "@tanstack/react-query";

interface SelectOfferDialogProps {
  accountId: number;
  currentOfferId?: string;
  trigger?: React.ReactNode;
  onOfferSelected?: () => void;
}

interface OfferOption {
  id: number;
  title: string;
}

export function SelectOfferDialog({
  accountId,
  currentOfferId,
  trigger,
  onOfferSelected,
}: SelectOfferDialogProps) {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedOfferId, setSelectedOfferId] = useState<string>("");
  const [localAccounts, setLocalAccounts] = useState<LocalAccount[]>([]);
  const [currentAccount, setCurrentAccount] = useState<LocalAccount | null>(
    null
  );
  const [isSaving, setIsSaving] = useState(false);

  // Fetch all offers using TanStack Query
  const {
    data: offersData,
    isLoading: isLoadingOffers,
    isError: isOffersError,
    isFetching,
  } = useQuery({
    queryKey: ["offers"],
    queryFn: async () => {
      // No need to cast here anymore, the type is inferred from offersService.getOffers
      const response = await offersService.getOffers({});
      return response;
    },
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });

  // Fetch local accounts
  useEffect(() => {
    if (dialogOpen) {
      const fetchAccounts = async () => {
        try {
          const accounts = await localAccountsDb.getAccounts();
          setLocalAccounts(accounts);

          // Find and set the current account
          const account = accounts.find((acc) => acc.id === accountId);
          if (account) {
            setCurrentAccount(account);
          }
        } catch (error) {
          console.error("Error loading accounts:", error);
        }
      };
      fetchAccounts();
    }
  }, [dialogOpen, accountId]);

  // Create memoized list of available offers
  const availableOffers = useMemo(() => {
    if (!offersData || !localAccounts.length) return [];

    // Map offers to OfferOption format
    const allOffers: OfferOption[] = offersData.data.items.map((offer) => ({
      id: offer.offerId,
      title: offer.title,
    }));

    // Create set of bound offer IDs (excluding current account's offer)
    const boundOfferIds = new Set(
      localAccounts
        .filter((account) => account.id !== accountId && account.offerId)
        .map((account) => account.offerId)
    );

    // Filter available offers
    const filteredOffers = allOffers.filter(
      (offer) => !boundOfferIds.has(offer.id.toString())
    );

    // If current account has an offer ID, ensure it's included
    if (currentOfferId) {
      const currentOffer = allOffers.find(
        (offer) => offer.id.toString() === currentOfferId
      );
      if (
        currentOffer &&
        !filteredOffers.some((o) => o.id === currentOffer.id)
      ) {
        return [...filteredOffers, currentOffer];
      }
    }

    return filteredOffers;
  }, [offersData, localAccounts, accountId, currentOfferId]);

  useEffect(() => {
    if (dialogOpen && currentOfferId) {
      setSelectedOfferId(currentOfferId);
    }
  }, [dialogOpen, currentOfferId]);

  const handleOpenChange = (open: boolean) => {
    setDialogOpen(open);
    if (!open) {
      setSelectedOfferId(currentOfferId || "");
    }
  };

  const handleSave = async () => {
    if (!accountId) return;

    setIsSaving(true);
    try {
      // Get the current account
      const accounts = await localAccountsDb.getAccounts();
      const account = accounts.find((acc) => acc.id === accountId);

      // Get price from selected offer
      let priceValue;
      if (selectedOfferId && offersData?.data?.items) {
        const selectedOffer = offersData.data.items.find(
          (offer) => offer.offerId.toString() === selectedOfferId
        );
        if (selectedOffer && selectedOffer.totalPrice) {
          // Convert string price to number
          const totalPriceStr = selectedOffer.totalPrice.replace(
            /[^0-9.]/g,
            ""
          );
          priceValue = parseFloat(totalPriceStr);
        }
      }

      if (account) {
        // Update the account with the new offer ID, price, listedDate and set status to 'listed'
        await localAccountsDb.updateAccount({
          ...account,
          offerId: selectedOfferId || undefined,
          status: selectedOfferId ? "listed" : account.status,
          listedDate: selectedOfferId ? new Date() : account.listedDate,
          price: priceValue,
        });

        handleOpenChange(false);
        onOfferSelected?.();
      }
    } catch (error) {
      console.error("Error updating account:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleUnbind = async () => {
    if (!accountId) return;

    setIsSaving(true);
    try {
      const accounts = await localAccountsDb.getAccounts();
      const account = accounts.find((acc) => acc.id === accountId);

      if (account) {
        await localAccountsDb.updateAccount({
          ...account,
          offerId: undefined,
        });

        handleOpenChange(false);
        onOfferSelected?.();
      }
    } catch (error) {
      console.error("Error unbinding account:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const defaultTrigger = (
    <Button variant="ghost" size="sm" className="h-7 w-7">
      <Link className="h-4 w-4" />
    </Button>
  );

  const isLoading = isLoadingOffers || !localAccounts;

  return (
    <Dialog open={dialogOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px] bg-background border-border">
        <DialogHeader>
          <DialogTitle>Select Offer to Bind</DialogTitle>
          {currentAccount?.notes && (
            <DialogDescription className="mt-2 text-sm text-muted-foreground">
              Account notes: {currentAccount.notes}
            </DialogDescription>
          )}
        </DialogHeader>

        <div className="py-4">
          <div className="space-y-2">
            <Label htmlFor="offer-select">Available Offers</Label>
            <Select
              value={selectedOfferId}
              onValueChange={setSelectedOfferId}
              disabled={isLoading}
            >
              <SelectTrigger id="offer-select" className="w-full">
                <SelectValue
                  placeholder={
                    isLoading ? "Loading offers..." : "Select an offer"
                  }
                />
              </SelectTrigger>
              <SelectContent>
                {isOffersError ? (
                  <SelectItem value="error" disabled>
                    Error loading offers
                  </SelectItem>
                ) : availableOffers.length === 0 && !isLoading ? (
                  <SelectItem value="no-offers" disabled>
                    No available offers found
                  </SelectItem>
                ) : (
                  availableOffers.map((offer) => (
                    <SelectItem key={offer.id} value={offer.id.toString()}>
                      {offer.title} (ID: {offer.id})
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex justify-between gap-2 pt-2">
          {currentOfferId && (
            <Button
              type="button"
              variant="outline"
              onClick={handleUnbind}
              disabled={isSaving}
            >
              Unbind
            </Button>
          )}
          <div className="flex justify-end gap-2 ml-auto">
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={
                isSaving || isLoading || (!selectedOfferId && !!currentOfferId)
              }
            >
              {isSaving ? "Saving..." : "Save"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
