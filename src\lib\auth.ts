/**
 * Auth service for managing authentication state with cookie-based authentication
 */

export const authService = {
  /**
   * Check if the user has the Eldorado authentication cookie
   * @returns True if the __Host-EldoradoIdToken cookie exists, false otherwise
   */
  isAuthenticated: (): boolean => {
    // Check if the Eldorado ID token cookie exists
    return document.cookie.includes("__Host-EldoradoIdToken=");
  },

  /**
   * Get the Eldorado ID token from cookies
   * @returns The ID token value or null if not found
   */
  getIdToken: (): string | null => {
    const cookies = document.cookie.split(';');
    const idTokenCookie = cookies.find(cookie =>
      cookie.trim().startsWith('__Host-EldoradoIdToken=')
    );

    if (idTokenCookie) {
      return idTokenCookie.split('=')[1];
    }

    return null;
  },

  /**
   * Get the XSRF token from cookies (needed for CSRF protection)
   * @returns The XSRF token value or null if not found
   */
  getXsrfToken: (): string | null => {
    const cookies = document.cookie.split(';');
    const xsrfTokenCookie = cookies.find(cookie =>
      cookie.trim().startsWith('__Host-XSRF-TOKEN=')
    );

    if (xsrfTokenCookie) {
      return xsrfTokenCookie.split('=')[1];
    }

    return null;
  },

  /**
   * Clear authentication by removing cookies (logout)
   * Note: This may not work for HttpOnly cookies set by the server
   */
  clearAuth: (): void => {
    // Attempt to clear the cookies by setting them to expire
    document.cookie = "__Host-EldoradoIdToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; samesite=strict";
    document.cookie = "__Host-EldoradoRefreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; samesite=strict";
    document.cookie = "__Host-XSRF-TOKEN=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; samesite=strict";
  },

  // Legacy methods for backward compatibility (deprecated)
  /**
   * @deprecated Use isAuthenticated() instead
   */
  getToken: (): string | null => {
    return this.getIdToken();
  },

  /**
   * @deprecated Cookie-based auth doesn't require manual token setting
   */
  setToken: (token: string): void => {
    console.warn("setToken is deprecated for cookie-based authentication");
  },

  /**
   * @deprecated Use clearAuth() instead
   */
  clearToken: (): void => {
    this.clearAuth();
  },
};
