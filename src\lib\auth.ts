/**
 * Auth service for managing authentication token and state
 */

const AUTH_TOKEN_KEY = "authToken";

export const authService = {
  /**
   * Set the authentication token
   * @param token The authentication token to store
   */
  setToken: (token: string): void => {
    localStorage.setItem(AUTH_TOKEN_KEY, token);
  },

  /**
   * Get the current authentication token
   * @returns The current authentication token or null if not authenticated
   */
  getToken: (): string | null => {
    return localStorage.getItem(AUTH_TOKEN_KEY);
  },

  /**
   * Check if the user is currently authenticated
   * @returns True if authenticated, false otherwise
   */
  isAuthenticated: (): boolean => {
    return !!localStorage.getItem(AUTH_TOKEN_KEY);
  },

  /**
   * Remove the authentication token (logout)
   */
  clearToken: (): void => {
    localStorage.removeItem(AUTH_TOKEN_KEY);
  },
};
