import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { offersService } from "@/lib/api";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import Editor, { ContentEditableEvent } from "react-simple-wysiwyg";
import { toast } from "sonner";
import { ImageUploader } from "@/components/ImageUploader";
import { SelectLocalAccountDialog } from "@/components/SelectLocalAccountDialog";
import { LocalAccount } from "@/lib/db";
import { useAtom } from 'jotai';
import { draftOffersAtom, draftHelpers } from "@/lib/atoms";
import { localAccountsDb } from "@/lib/db";

import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, Eye, EyeOff, SendIcon, Users, X, Save, Settings } from "lucide-react";
import { FillWithAIButton } from "@/components/FillWithAIButton";
import { TitlePresets } from "@/components/TitlePresets";

import type {
  OfferDetailsData,
  CreateOfferParams,
  AutoDeliveryInfo,
} from "@/lib/api";
import { encryptPassword } from "@/lib/utils";

export default function CreateOfferPage() {
  const navigate = useNavigate();
  const location = useLocation();
  const queryClient = useQueryClient();
  const [showPassword, setShowPassword] = useState(false);
  const [selectedAccountNote, setSelectedAccountNote] = useState<string | null>(
    null
  );
  const [selectedAccount, setSelectedAccount] = useState<LocalAccount | null>(null);
  const [draftOffers, setDraftOffers] = useAtom(draftOffersAtom);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // Default form data
  const defaultFormData: Partial<OfferDetailsData> = {
    title: "",
    offerDesc: "<p>Enter description here...</p>",
    price: 52.99,
    offerDuration: 30, // Default 30 days
    freeInsurance: 7, // Default 7 days
    gameId: 5567, // Default game ID
    serverId: 5568, // Default server ID
    categoryId: 5568, // Default category ID
    isAuto: true,
    autoDelivery: {
      loginName: "",
      characterName: "Unspecified Character Name", // Default value
      password: "",
      retypePassword: "",
      isInfoSame: true,
      choose5: true,
      original: {
        firstName: "not specified",
        lastName: "not specified",
        phone: "*********",
        email: "<EMAIL>",
        city: "not specified",
        country: "not specified",
        address: "",
        zipcode: "",
        state: "",
        birthday: "",
        question: "",
        answer: "",
      },
      current: {
        firstName: "",
        lastName: "",
        phone: "*********",
        email: "<EMAIL>",
        city: "not specified",
        country: "not specified",
        address: "",
        zipcode: "",
        state: "",
      },
      battleNetLoginName: "",
      instruction: "",
      securityQuestion: "",
      securityAnswer: "",
      firstCDKey: "",
      parentalPassword: "",
      choose1: false,
    },
  };

  // State for form data, explicitly typed
  const [formData, setFormData] = useState<Partial<OfferDetailsData>>(defaultFormData);

  // Check if we're editing a draft
  const [currentDraftId, setCurrentDraftId] = useState<string | null>(null);

  // Load draft if draftId is provided in URL
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const draftId = params.get('draftId');

    if (draftId && draftOffers[draftId]) {
      // We're editing an existing draft
      const draft = draftOffers[draftId];
      setFormData(draft);
      setCurrentDraftId(draftId);
      toast.info("Loaded draft for editing");
    }
  }, [location.search, draftOffers]);

  // State for password retyping
  const [retypePassword, setRetypePassword] = useState<string>("");

  // Handle input changes, handling nested autoDelivery fields
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    field: string
  ) => {
    const { value } = e.target;
    setFormData((prev) => {
      const keys = field.split(".");
      if (keys.length > 1 && keys[0] === "autoDelivery") {
        const subField = keys[1];
        // Create a new partial autoDelivery object safely
        const updatedAutoDelivery: Partial<AutoDeliveryInfo> = {
          ...(prev.autoDelivery || {}),
          [subField]: value,
        };
        return {
          ...prev,
          autoDelivery: updatedAutoDelivery,
        };
      }
      // Ensure numeric fields are stored as numbers if needed by API
      const isNumericField = [
        "offerDuration",
        "freeInsurance",
        "price",
        "gameId",
        "serverId",
        "categoryId",
      ].includes(field);
      const parsedValue = isNumericField ? parseFloat(value) || 0 : value;
      return { ...prev, [field]: parsedValue };
    });
  };

  // Handle description changes specifically
  const handleDescriptionChange = (e: ContentEditableEvent) => {
    setFormData((prev) => ({
      ...prev,
      offerDesc: e.target.value,
    }));
  };

  // Function to extract current image URL from description
  const getCurrentImageUrl = (): string | null => {
    const descriptionHtml = formData?.offerDesc || "";
    const imgMatch = descriptionHtml.match(/<img[^>]*id=["']mainOfferImg["'][^>]*src=["']([^"']*)["'][^>]*>/i);
    return imgMatch ? imgMatch[1] : null;
  };

  // Function to inject image into description if one exists
  const injectImageIntoDescription = (description: string): string => {
    const currentImageUrl = getCurrentImageUrl();
    if (!currentImageUrl) {
      return description;
    }

    // Check if the new description already has the mainOfferImg
    if (description.includes('id="mainOfferImg"')) {
      // Update existing image with current URL
      return description.replace(
        /<img[^>]*id=["']mainOfferImg["'][^>]*>/i,
        `<img id="mainOfferImg" src="${currentImageUrl}" alt="Main offer image" style="max-width: 100%; height: auto; margin: 10px 0;">`
      );
    } else {
      // Inject image at the beginning of the description
      const imgTag = `<img id="mainOfferImg" src="${currentImageUrl}" alt="Main offer image" style="max-width: 100%; height: auto; margin: 10px 0;" />`;
      return `${imgTag}\n${description}`;
    }
  };

  // Method to update main offer image in description
  const updateMainOfferImage = (imageUrl: string) => {
    // Get the current description HTML
    const descriptionHtml = formData?.offerDesc || "";

    // Check if the image with id "mainOfferImg" exists in the description
    if (descriptionHtml.includes('id="mainOfferImg"')) {
      // Create a safer approach to update the image
      let updatedHtml = descriptionHtml;

      try {
        // First approach: Try to handle malformed HTML with regex
        // This searches for an img tag with id="mainOfferImg" and replaces its src
        updatedHtml = descriptionHtml.replace(
          /<img[^>]*id=["']mainOfferImg["'][^>]*(?:src=["'][^"']*["'])?[^>]*>/i,
          `<img id="mainOfferImg" src="${imageUrl}" alt="Main offer image" style="max-width: 100%; height: auto; margin: 10px 0;">`
        );

        // If no replacement happened, try a more aggressive approach
        if (updatedHtml === descriptionHtml) {
          console.log("First replacement approach failed, trying alternative");
          // Find the position of the mainOfferImg id
          const idPosition = descriptionHtml.indexOf('id="mainOfferImg"');
          if (idPosition > 0) {
            // Find the img tag start
            const tagStart = descriptionHtml.lastIndexOf("<img", idPosition);
            // Find the tag end (allowing for malformed tags)
            let tagEnd = descriptionHtml.indexOf(">", idPosition);
            if (tagEnd === -1) tagEnd = descriptionHtml.length;

            // Extract the entire img tag
            const originalImgTag = descriptionHtml.substring(
              tagStart,
              tagEnd + 1
            );
            console.log("Found original tag:", originalImgTag);

            // Replace with well-formed tag
            const newImgTag = `<img id="mainOfferImg" src="${imageUrl}" alt="Main offer image" style="max-width: 100%; height: auto; margin: 10px 0;">`;
            updatedHtml = descriptionHtml.replace(originalImgTag, newImgTag);
          }
        }
      } catch (err) {
        console.error("Error updating image in HTML:", err);
        // Fallback: append the image
        const imgTag = `<img id="mainOfferImg" src="${imageUrl}" alt="Main offer image" style="max-width: 100%; height: auto; margin: 10px 0;" />`;
        updatedHtml = `${descriptionHtml}\n${imgTag}`;
      }

      // Update the description in the state
      setFormData((prev) => ({
        ...prev,
        offerDesc: updatedHtml,
      }));

      toast.success("Main image updated successfully");
    } else {
      // If no image with the ID exists, append one to the description
      const imgTag = `<img id="mainOfferImg" src="${imageUrl}" alt="Main offer image" style="max-width: 100%; height: auto; margin: 10px 0;" />`;
      const updatedHtml = `${descriptionHtml}\n${imgTag}`;

      setFormData((prev) => ({
        ...prev,
        offerDesc: updatedHtml,
      }));

      toast.success("Main image added to description");
    }
  };

  // Handle back button
  const handleBack = () => {
    // Refresh offers list when navigating back
    queryClient.invalidateQueries({ queryKey: ["offers"] });
    navigate("/");
  };

  // Handle save as draft
  const handleSaveAsDraft = () => {
    console.log("💾 ~ handleSaveAsDraft: Saving offer as draft...");

    if (!formData) {
      toast.error("Cannot save draft, form data is missing");
      return;
    }

    if (currentDraftId && draftOffers[currentDraftId]) {
      // We're updating an existing draft
      const existingDraft = draftOffers[currentDraftId];
      const updatedDraft = draftHelpers.updateDraft(existingDraft, formData);

      // Save the updated draft to local storage
      setDraftOffers((prev) => draftHelpers.saveDraft(prev, updatedDraft));
      toast.success("Draft updated");
    } else {
      // Create a new draft from the current form data
      const draft = draftHelpers.createDraft(formData);

      // Save the draft to local storage
      setDraftOffers((prev) => draftHelpers.saveDraft(prev, draft));
      toast.success("Offer saved as draft");
    }

    navigate("/");
  };

  // Mutation for creating the offer
  const createMutation = useMutation({
    mutationFn: (params: CreateOfferParams) => {
      console.log("🚀 ~ mutationFn: Creating offer with params:", params);
      return offersService.createAccount(params);
    },
    onSuccess: async (data) => {
      console.log("✅ ~ onSuccess: Offer created successfully!", data);
      toast.success("Offer created successfully!");

      // Auto-bind the selected account to the newly created offer
      if (selectedAccount && selectedAccount.id && data.data?.offerId) {
        try {
          const updatedAccount = {
            ...selectedAccount,
            offerId: data.data.offerId.toString(),
            status: "listed" as const,
            listedDate: new Date(),
            price: formData?.price || selectedAccount.price,
          };

          await localAccountsDb.updateAccount(updatedAccount);
          console.log("✅ ~ Auto-bound account to offer:", selectedAccount.email, "->", data.data.offerId);
          toast.success(`Account ${selectedAccount.email} automatically bound to offer!`);
        } catch (error) {
          console.error("❌ ~ Failed to auto-bind account:", error);
          toast.error("Offer created but failed to bind account automatically");
        }
      }

      // Invalidate all offers queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["offers"] });
      // Navigate to the newly created offer
      if (data.data?.offerId) {
        navigate(`/offers/${data.data.offerId}`);
      } else {
        navigate("/");
      }
    },
    onError: (err) => {
      console.error("❌ ~ onError: Failed to create offer:", err);
      toast.error(
        `Failed to create offer: ${err instanceof Error ? err.message : "Unknown error"
        }`
      );
    },
  });

  // Handle publish button click
  const handlePublish = () => {
    console.log("💾 ~ handlePublish: Attempting to create offer...");
    console.log("💾 ~ handlePublish: Current formData:", formData);

    if (!formData) {
      console.warn("💾 ~ handlePublish: Create cancelled - data missing.");
      toast.error("Cannot create offer, essential data is missing.");
      return;
    }

    // Prepare the payload
    const payload = {
      ...formData,
      offerId: null, // Ensure offerId is null for create
      screenShot:
        "https://image-cdn-p.azureedge.net/offer-image/mop1213/20250412160541541.png",
      agreeCheck: true,
      actionType: "",
    };

    // Encrypt passwords before sending
    if (payload.autoDelivery?.password) {
      payload.autoDelivery = {
        ...payload.autoDelivery,
        password: encryptPassword(payload.autoDelivery.password),
        retypePassword: encryptPassword(payload.autoDelivery.password),
        characterName: "Unspecified Character Name", // Always set this value on submit
        isInfoSame: true,
        choose5: true,
      };
    }

    // Validation
    if (!payload.title?.trim()) {
      console.warn("💾 ~ handlePublish: Create cancelled - title empty.");
      toast.error("Offer title cannot be empty.");
      return;
    }
    if (payload.price === undefined || payload.price <= 0) {
      console.warn("💾 ~ handlePublish: Create cancelled - invalid price.");
      toast.error("Price must be a positive number.");
      return;
    }
    if (payload.offerDuration === undefined || payload.offerDuration <= 0) {
      console.warn("💾 ~ handlePublish: Create cancelled - invalid duration.");
      toast.error("Duration must be a positive number.");
      return;
    }

    console.log("💾 ~ handlePublish: Calling mutation with payload:", payload);
    createMutation.mutate({ payload });
  };

  // Update this function to store account notes when selected
  const handleAccountSelected = (account: LocalAccount) => {
    setFormData((prev) => ({
      ...prev,
      autoDelivery: {
        ...(prev.autoDelivery || {}),
        loginName: account.email,
        password: account.password,
      },
    }));
    setSelectedAccountNote(account.notes || null);
    setSelectedAccount(account); // Store the selected account for auto-binding
    toast.success(`Account ${account.email} selected`);
  };

  // Add a function to clear the selected account
  const clearSelectedAccount = () => {
    setFormData((prev) => ({
      ...prev,
      autoDelivery: {
        ...(prev.autoDelivery || {}),
        loginName: "",
        password: "",
      },
    }));
    setSelectedAccountNote(null);
    setSelectedAccount(null); // Clear the selected account
    toast.success("Account selection cleared");
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-5xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleBack}
              className="mr-4"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-xl pe-3 font-bold tracking-tight">
                {currentDraftId ? 'Edit Draft Offer' : 'Create New Offer'}
              </h1>
            </div>
          </div>
          {/* Action Buttons in Header */}
          <div className="flex gap-3">
            <Button
              onClick={handleSaveAsDraft}
              variant="outline"
              size="lg"
              className="rounded-full"
            >
              <Save className="mr-2 h-4 w-4" /> {currentDraftId ? 'Update Draft' : 'Save as Draft'}
            </Button>
            <Button
              onClick={handlePublish}
              disabled={createMutation.isPending}
              size="lg" // Larger button
              className="rounded-full" // Rounded style
            >
              {createMutation.isPending ? (
                "Publishing..."
              ) : (
                <>
                  <SendIcon className="mr-2 h-4 w-4" /> Publish
                </>
              )}
            </Button>
          </div>
        </div>

        <div className="space-y-8">
          {/* Auto Delivery Information Card - Now on top */}
          <Card>
            <CardHeader>
              <CardTitle>Auto Delivery Information</CardTitle>
              <CardDescription>
                Information required for automated delivery
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Login Name and Character Name */}
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="loginName">Login Name</Label>
                  <div className="flex gap-2">
                    <Input
                      id="loginName"
                      value={formData?.autoDelivery?.loginName || ""}
                      onChange={(e) =>
                        handleChange(e, "autoDelivery.loginName")
                      }
                      className="h-12 flex-1"
                      autoComplete="username"
                    />
                    <SelectLocalAccountDialog
                      accountTypeFilter="player-auctions"
                      onAccountSelected={handleAccountSelected}
                      trigger={
                        <Button
                          variant="outline"
                          className="h-12 gap-1.5 whitespace-nowrap"
                        >
                          <Users className="h-4 w-4" />
                          Select Account
                        </Button>
                      }
                    />
                  </div>
                  {selectedAccountNote && (
                    <div className="mt-2 text-sm bg-muted/30 p-2 rounded border border-border relative">
                      <span className="font-medium text-xs text-muted-foreground">
                        Account Note:
                      </span>{" "}
                      {selectedAccountNote}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 absolute right-1 top-1 text-muted-foreground hover:text-destructive"
                        onClick={clearSelectedAccount}
                      >
                        <X className="h-3.5 w-3.5" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              {/* Password Input */}
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={formData?.autoDelivery?.password || ""}
                      onChange={(e) => handleChange(e, "autoDelivery.password")}
                      className="h-12 pr-10"
                      autoComplete="new-password"
                      placeholder="Account password"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-1 top-1/2 transform -translate-y-1/2 h-10 w-10 text-muted-foreground hover:text-foreground"
                      onClick={() => setShowPassword(!showPassword)}
                      aria-label={
                        showPassword ? "Hide password" : "Show password"
                      }
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  {formData?.autoDelivery?.password && selectedAccountNote && (
                    <div className="mt-2 text-xs text-muted-foreground">
                      Password was automatically filled from the selected
                      account.
                    </div>
                  )}
                </div>
              </div>

              {/* Advanced Options Toggle */}
              <div className="flex items-center justify-end">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  {showAdvancedOptions ? 'Hide Advanced Options' : 'Show Advanced Options'}
                </Button>
              </div>

              {/* Delivery Instructions - Hidden behind toggle */}
              {showAdvancedOptions && (
                <div className="space-y-2">
                  <Label htmlFor="deliveryInstructions">
                    Delivery Instructions
                  </Label>
                  <Textarea
                    id="deliveryInstructions"
                    value={formData?.autoDelivery?.instruction || ""}
                    onChange={(e) => handleChange(e, "autoDelivery.instruction")}
                    rows={5}
                    className="resize-y"
                    placeholder="Provide clear steps for delivery..."
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Basic Information Card - Now below Auto Delivery */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                General information about your offer
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="title">Title</Label>
                  <div className="flex items-center gap-2">
                    <TitlePresets
                      currentTitle={formData?.title || ""}
                      onTitleSelect={(title) => {
                        setFormData((prev) => ({
                          ...prev,
                          title,
                        }));
                      }}
                    />
                    <FillWithAIButton
                      inputText={formData?.title || ""}
                      onTitleGenerated={(title) => {
                        setFormData((prev) => ({
                          ...prev,
                          title,
                        }));
                      }}
                      onDescriptionGenerated={(description) => {
                        const descriptionWithImage = injectImageIntoDescription(description);
                        setFormData((prev) => ({
                          ...prev,
                          offerDesc: descriptionWithImage,
                        }));
                      }}
                    />
                  </div>
                </div>
                <Input
                  id="title"
                  value={formData?.title || ""}
                  onChange={(e) => handleChange(e, "title")}
                  className="h-12"
                />
              </div>

              {/* Description Editor/Textarea */}
              <div className="space-y-2 ">
                <div className="flex items-center justify-between mb-2">
                  <Label htmlFor="offerDesc">Description</Label>
                  <div className="flex items-center space-x-2"></div>
                </div>
                <div className="flex-grow overflow-y-auto border rounded-md">
                  <Editor
                    value={formData?.offerDesc || ""}
                    onChange={handleDescriptionChange}
                    containerProps={{
                      style: {
                        height: "100%",
                        minHeight: "500px",
                        resize: "vertical",
                        border: "none",
                      },
                    }}
                    className="bg-background h-[600px] overflow-y-auto"
                  />
                </div>
              </div>

              {/* Image Upload */}
              <div className="space-y-2">
                <Label htmlFor="imageUpload">
                  Offer's Main Image (description)
                </Label>
                <div className="flex items-center gap-4">
                  <ImageUploader
                    className="h-50 w-100 min-h-0 p-3"
                    onUploadSuccess={(data) => {
                      const imageUrl = data.sasUri;
                      console.log("🚀 ~ onUploadSuccess: ", imageUrl);
                      updateMainOfferImage(imageUrl);
                    }}
                    onUploadError={(error) => {
                      toast.error(`Upload failed: ${error.message}`);
                    }}
                    autoNavigateToFolder={
                      formData?.autoDelivery?.loginName
                        ? formData.autoDelivery.loginName.split("@")[0]
                        : undefined
                    }
                  />
                </div>
              </div>

              {/* Duration and Insurance Inputs */}
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="offerDuration">Duration (days)</Label>
                  <Input
                    id="offerDuration"
                    type="number"
                    value={formData?.offerDuration || 30}
                    onChange={(e) => handleChange(e, "offerDuration")}
                    className="h-12"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="freeInsurance">Free Insurance</Label>
                  <Input
                    id="freeInsurance"
                    type="number"
                    value={formData?.freeInsurance || 7}
                    onChange={(e) => handleChange(e, "freeInsurance")}
                    className="h-12"
                  />
                </div>
              </div>

              {/* Price Input */}
              <div className="space-y-2">
                <Label htmlFor="price">Price ($)</Label>
                <Input
                  id="price"
                  type="number"
                  step="0.01"
                  value={formData?.price || 0}
                  onChange={(e) => handleChange(e, "price")}
                  className="h-12"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
