import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { BookmarkPlus, BookmarkCheck, X } from "lucide-react";
import { toast } from "sonner";

interface TitlePresetsProps {
  currentTitle: string;
  onTitleSelect: (title: string) => void;
}

export function TitlePresets({ currentTitle, onTitleSelect }: TitlePresetsProps) {
  const [presets, setPresets] = useState<string[]>([]);
  const [isOpen, setIsOpen] = useState(false);

  // Load presets from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem("titlePresets");
    if (saved) {
      try {
        setPresets(JSON.parse(saved));
      } catch (e) {
        console.error("Failed to parse saved presets");
      }
    }
  }, []);

  // Save current title as preset
  const savePreset = () => {
    if (!currentTitle.trim()) {
      toast.error("Title is empty");
      return;
    }

    if (presets.includes(currentTitle)) {
      toast.error("Preset already exists");
      return;
    }

    const newPresets = [...presets, currentTitle];
    setPresets(newPresets);
    localStorage.setItem("titlePresets", JSON.stringify(newPresets));
    toast.success("Title preset saved");
  };

  // Load preset
  const loadPreset = (title: string) => {
    onTitleSelect(title);
    setIsOpen(false);
    toast.success("Preset loaded");
  };

  // Delete preset
  const deletePreset = (title: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent the load action
    const newPresets = presets.filter(preset => preset !== title);
    setPresets(newPresets);
    localStorage.setItem("titlePresets", JSON.stringify(newPresets));
    toast.success("Preset deleted");
  };

  return (
    <div className="flex gap-2">
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={savePreset}
        className="gap-1"
      >
        <BookmarkPlus className="h-4 w-4" />
        Save Preset
      </Button>

      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="gap-1"
            disabled={presets.length === 0}
          >
            <BookmarkCheck className="h-4 w-4" />
            Load Preset
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-2" align="end">
          <div className="space-y-1">
            {presets.length === 0 ? (
              <p className="text-sm text-muted-foreground p-2">No presets saved</p>
            ) : (
              presets.sort((a, b) => a.localeCompare(b)).map((preset, index) => (
                <div
                  key={index}
                  className="flex items-center w-full rounded hover:bg-accent hover:text-accent-foreground transition-colors group"
                >
                  <button
                    onClick={() => loadPreset(preset)}
                    className="flex-1 text-left p-2 text-sm truncate"
                    title={preset}
                  >
                    {preset}
                  </button>
                  <button
                    onClick={(e) => deletePreset(preset, e)}
                    className="p-1 mr-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-destructive hover:text-destructive-foreground rounded"
                    title="Delete preset"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))
            )}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
} 