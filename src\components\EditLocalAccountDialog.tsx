import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Pencil } from "lucide-react";
import { LocalAccountForm } from "@/components/LocalAccountForm";
import { LocalAccount } from "@/lib/db";

interface EditLocalAccountDialogProps {
  account: LocalAccount;
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onAccountUpdated?: () => void;
}

export function EditLocalAccountDialog({
  account,
  trigger,
  open,
  onOpenChange,
  onAccountUpdated,
}: EditLocalAccountDialogProps) {
  const [dialogOpen, setDialogOpen] = React.useState(open || false);

  const handleOpenChange = (open: boolean) => {
    setDialogOpen(open);
    onOpenChange?.(open);
  };

  const handleSuccess = () => {
    handleOpenChange(false);
    onAccountUpdated?.();
  };

  const defaultTrigger = (
    <Button
      variant="ghost"
      size="icon"
      className="h-7 w-7 hover:bg-accent hover:text-accent-foreground"
      title="Edit account"
    >
      <Pencil className="h-4 w-4" />
    </Button>
  );

  return (
    <Dialog open={dialogOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px] border-border bg-background">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-lg font-semibold">
            Edit Account
          </DialogTitle>
          <DialogDescription className="text-muted-foreground text-sm">
            Make changes to your account details below.
          </DialogDescription>
        </DialogHeader>
        <LocalAccountForm
          initialData={account}
          onSuccess={handleSuccess}
          onCancel={() => handleOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
