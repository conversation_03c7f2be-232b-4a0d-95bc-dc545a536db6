import { atom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';
import { OfferDetailsData } from './api';

// Type for draft offers
export interface DraftOffer extends Partial<OfferDetailsData> {
  id: string; // Unique ID for the draft
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  isDraft: true; // Flag to identify as draft
}

// Type for the drafts collection
export interface DraftOffers {
  [id: string]: DraftOffer;
}

// Type for AI settings
export interface AISettings {
  apiKey: string;
  titleModel: string;
  descriptionModel: string;
}

// Default AI settings
const defaultAISettings: AISettings = {
  apiKey: '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',
  titleModel: 'google/gemini-2.5-flash-preview-05-20',
  descriptionModel: 'google/gemini-2.5-flash-preview-05-20',
};

// Atom for storing draft offers in local storage
export const draftOffersAtom = atomWithStorage<DraftOffers>('draft-offers', {});

// Atom for the current draft being edited
export const currentDraftAtom = atom<DraftOffer | null>(null);

// Atom for storing AI settings in local storage
export const aiSettingsAtom = atomWithStorage<AISettings>('ai-settings', defaultAISettings);

// Types for local accounts filters
export type TimelineFilter = "1month" | "prevmonth" | "1week" | "all";
export type StateFilter = "all-except-sold" | "all" | "sold";

// Atoms for storing local accounts filter preferences in local storage
export const localAccountsTimelineFilterAtom = atomWithStorage<TimelineFilter>('local-accounts-timeline-filter', '1month');
export const localAccountsStateFilterAtom = atomWithStorage<StateFilter>('local-accounts-state-filter', 'all');

// Helper functions for draft management
export const draftHelpers = {
  // Create a new draft from form data
  createDraft: (formData: Partial<OfferDetailsData>): DraftOffer => {
    const now = new Date().toISOString();
    return {
      ...formData,
      id: `draft-${Date.now()}`,
      createdAt: now,
      updatedAt: now,
      isDraft: true,
    };
  },

  // Update an existing draft
  updateDraft: (draft: DraftOffer, formData: Partial<OfferDetailsData>): DraftOffer => {
    return {
      ...draft,
      ...formData,
      updatedAt: new Date().toISOString(),
    };
  },

  // Save a draft to storage
  saveDraft: (
    drafts: DraftOffers,
    draft: DraftOffer
  ): DraftOffers => {
    return {
      ...drafts,
      [draft.id]: draft,
    };
  },

  // Delete a draft from storage
  deleteDraft: (
    drafts: DraftOffers,
    draftId: string
  ): DraftOffers => {
    const newDrafts = { ...drafts };
    delete newDrafts[draftId];
    return newDrafts;
  },

  // Get all drafts as an array
  getDraftsArray: (drafts: DraftOffers): DraftOffer[] => {
    return Object.values(drafts).sort(
      (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    );
  },
};
