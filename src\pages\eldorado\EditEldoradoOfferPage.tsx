import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { ArrowLeft, Save } from "lucide-react";
import { EldoradoFillWithAIButton } from "@/components/EldoradoFillWithAIButton";

// Placeholder for actual data fetching/structure
interface EldoradoOffer {
  id: string;
  title: string;
  description: string;
}

export default function EditEldoradoOfferPage() {
  const { offerId } = useParams<{ offerId: string }>();
  const navigate = useNavigate();
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Placeholder for fetching offer data
    if (offerId) {
      setIsLoading(true);
      // Simulate API call
      setTimeout(() => {
        // Replace with actual data fetching logic
        const fetchedOffer: EldoradoOffer | null = {
          id: offerId,
          title: `Sample Eldorado Offer ${offerId}`,
          description: `This is a sample description for Eldorado offer ${offerId}.`,
        };

        if (fetchedOffer) {
          setTitle(fetchedOffer.title);
          setDescription(fetchedOffer.description);
        } else {
          toast.error(`Eldorado offer with ID ${offerId} not found.`);
          navigate("/"); // Or to an error page
        }
        setIsLoading(false);
      }, 1000);
    } else {
      toast.error("No offer ID provided.");
      navigate("/");
    }
  }, [offerId, navigate]);

  const handleSave = () => {
    if (!title.trim() || !description.trim()) {
      toast.error("Please fill in both title and description.");
      return;
    }
    // Placeholder for actual update logic
    console.log("Updating Eldorado Offer:", { offerId, title, description });
    toast.success(`Eldorado offer ${offerId} updated (placeholder)`);
    navigate("/"); // Navigate back to main page for now
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background p-4 md:p-8 flex items-center justify-center">
        <p>Loading offer details...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-4 md:p-8">
      <Card className="max-w-3xl mx-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <CardTitle>Edit Eldorado Offer {offerId}</CardTitle>
            <div className="w-10"></div> {/* Spacer */}
          </div>
          <CardDescription>
            Update the details for your Eldorado offer.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="title">Title</Label>
              <EldoradoFillWithAIButton
                inputText={title}
                onTitleGenerated={setTitle}
                onDescriptionGenerated={setDescription}
              />
            </div>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter offer title"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter offer description"
              rows={25}
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => navigate(-1)}>
              Cancel
            </Button>
            <Button onClick={handleSave} className="gap-1">
              <Save className="h-4 w-4" />
              Save Changes
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}