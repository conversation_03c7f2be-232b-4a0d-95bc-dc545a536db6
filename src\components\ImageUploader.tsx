import { useState, useCallback, useEffect } from "react";
import { Upload, AlertCircle } from "lucide-react";
import { mediaService } from "../lib/api";
import { cn } from "../lib/utils";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { FileSystemExplorerDialog } from "./FileSystemExplorerDialog";
import { 
  isFileSystemAccessSupported,
  setupAccountsDirectory,
  hasAccountsDirectorySetup
} from "../lib/fileSystemAccess";

interface ImageUploaderProps {
  onUploadSuccess?: (data: { blobName: string; sasUri: string }) => void;
  onUploadError?: (error: Error) => void;
  autoNavigateToFolder?: string;
  className?: string;
}

export function ImageUploader({
  onUploadSuccess,
  onUploadError,
  autoNavigateToFolder,
  className,
}: ImageUploaderProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [showFileExplorer, setShowFileExplorer] = useState(false);
  const [hasAccountsDir, setHasAccountsDir] = useState(false);

  useEffect(() => {
    const checkAccountsDirectory = async () => {
      if (isFileSystemAccessSupported()) {
        const hasDir = await hasAccountsDirectorySetup();
        setHasAccountsDir(hasDir);
      }
    };
    checkAccountsDirectory();
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const processFile = async (file: File) => {
    // Validate file type
    if (!file.type.startsWith("image/")) {
      onUploadError?.(new Error("Only image files are allowed"));
      return;
    }

    // Show preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Upload file
    try {
      setIsUploading(true);
      const response = await mediaService.uploadImage({ file });

      if (response.isSuccess) {
        onUploadSuccess?.({
          blobName: response.data.blobName,
          sasUri: response.data.sasUri,
        });
      } else {
        throw new Error("Upload failed: " + JSON.stringify(response));
      }
    } catch (error) {
      console.error("Image upload error:", error);
      onUploadError?.(
        error instanceof Error ? error : new Error("Unknown upload error")
      );
    } finally {
      setIsUploading(false);
    }
  };

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);

      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        const file = e.dataTransfer.files[0];
        processFile(file);
      }
    },
    [onUploadSuccess, onUploadError]
  );

  const handleButtonClick = async () => {
    if (!isFileSystemAccessSupported()) {
      onUploadError?.(new Error("File System Access API is not supported in this browser. Please use a modern browser like Chrome, Edge, or Safari."));
      return;
    }

    // Check if we have directory access, if not, set it up first
    if (!hasAccountsDir) {
      try {
        const success = await setupAccountsDirectory();
        if (success) {
          setHasAccountsDir(true);
          setShowFileExplorer(true);
        }
      } catch (error) {
        console.error('Failed to setup accounts directory:', error);
        onUploadError?.(new Error('Failed to setup accounts directory'));
      }
    } else {
      setShowFileExplorer(true);
    }
  };

  const handleFilesSelected = (files: File[]) => {
    if (files.length > 0) {
      processFile(files[0]);
    }
  };

  return (
    <Card
      className={cn(
        "relative flex flex-col items-center justify-center p-6 border-2 border-dashed rounded-lg transition-all duration-200 min-h-[200px]",
        isDragging ? "border-primary bg-primary/5" : "border-border",
        previewUrl ? "bg-background/50" : "bg-background/30",
        className
      )}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {previewUrl ? (
        <div className="relative w-full h-full">
          <img
            src={previewUrl}
            alt="Preview"
            className="max-h-[300px] max-w-full mx-auto object-contain rounded-md"
          />
          <Button
            variant="outline"
            size="sm"
            className="absolute top-2 right-2 bg-background/80 backdrop-blur-sm"
            onClick={() => setPreviewUrl(null)}
          >
            Change
          </Button>
        </div>
      ) : (
        <>
          <div className="flex flex-col items-center mb-4">
            <div className="p-3 mb-4 rounded-full bg-primary/10">
              <Upload className="w-6 h-6 text-primary" />
            </div>
            <h3 className="mb-1 text-lg font-medium">Drag & drop image</h3>
            <p className="text-sm text-muted-foreground text-center">
              Drop your image here, or click to browse
            </p>
          </div>

          {!isFileSystemAccessSupported() ? (
            <div className="flex flex-col items-center gap-2 text-center">
              <AlertCircle className="w-8 h-8 text-destructive" />
              <p className="text-sm text-destructive font-medium">
                File System Access API not supported
              </p>
              <p className="text-xs text-muted-foreground">
                Please use Chrome, Edge, or Safari
              </p>
            </div>
          ) : (
            <Button
              variant="outline"
              onClick={handleButtonClick}
              disabled={isUploading}
            >
              {isUploading ? "Uploading..." : "Select Image"}
            </Button>
          )}
        </>
      )}

      {isUploading && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm rounded-lg">
          <div className="flex flex-col items-center">
            <div className="w-8 h-8 border-4 border-primary/30 border-t-primary rounded-full animate-spin mb-2"></div>
            <p className="text-sm font-medium">Uploading...</p>
          </div>
        </div>
      )}

      <FileSystemExplorerDialog
        open={showFileExplorer}
        onOpenChange={setShowFileExplorer}
        onFilesSelected={handleFilesSelected}
        accept={{
          'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp', '.svg']
        }}
        multiple={false}
        defaultPath="C:\Users\<USER>\Desktop\GoldfarmData\accs_sale\accounts"
        autoNavigateToFolder={autoNavigateToFolder}
        title="Select Image"
      />
    </Card>
  );
}
