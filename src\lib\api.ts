// Import auth service
import { authService } from "@/lib/auth";

// Default API configuration
const API_CONFIG = {
  baseUrl: "https://offer-api.playerauctions.com/api",
  defaultHeaders: {
    "Content-Type": "application/json",
    // Authorization header will be added dynamically
  },
};

// Helper function to get auth headers with token from auth service
const getAuthHeaders = () => {
  const headers: Record<string, string> = { ...API_CONFIG.defaultHeaders };
  const token = authService.getToken();

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  return headers;
};

// Define interfaces for API structures
interface OfferItem {
  offerId: number;
  systemStatus: string;
  title: string;
  gameName: string;
  totalPrice: string;
  expiredTimeString: string;
  productType: string;
  url: string;
}

interface OffersResponseData {
  count: number;
  items: OfferItem[];
}

interface OffersResponse {
  isSuccess: boolean;
  code: number;
  data: OffersResponseData;
}

// Interfaces for GetOfferDetails
export interface AutoDeliveryInfo {
  loginName: string;
  battleNetLoginName: string;
  password?: string;
  retypePassword?: string;
  characterName: string;
  securityQuestion?: string;
  securityAnswer?: string;
  firstCDKey?: string;
  parentalPassword?: string;
  isInfoSame: boolean;
  choose1: boolean;
  choose5: boolean;
  original: {
    firstName: string;
    lastName: string;
    phone: string;
    email: string;
    address: string;
    zipcode: string;
    city: string;
    state: string;
    country: string;
    birthday: string; // ISO Date string
    question: string;
    answer: string;
  };
  current: {
    firstName: string;
    lastName: string;
    phone: string;
    email: string;
    address: string;
    zipcode: string;
    city: string;
    state: string;
    country: string;
  };
  instruction: string;
}

export interface OfferDetailsData {
  categoryId: number;
  serverId: number;
  freeInsurance: number;
  instruction: string;
  isAuto: boolean;
  blobName: string;
  screenShot: string;
  autoDelivery: AutoDeliveryInfo;
  gameId: number;
  price: number;
  offerDuration: number;
  title: string;
  offerDesc: string; // HTML string
  offerId: number;
  isAgree: boolean;
  memberId: number;
  state: number;
  productType: string;
}

export interface OfferDetailsResponse {
  isSuccess: boolean;
  code: number;
  data: OfferDetailsData;
  message?: string;
}

export interface GetOfferDetailsParams {
  offerId: number;
}

export interface GetOffersParams {
  pageIndex?: number;
  pageSize?: number;
  sortField?: string | null; // Allow string or null
  sortOrder?: "asc" | "desc" | null; // Allow specific strings or null
}

// Add interface for update parameters
export interface UpdateOfferParams {
  offerId: number;
  payload: OfferDetailsData; // Use existing OfferDetailsData for the payload
}

// Add interface for create parameters
export interface CreateOfferParams {
  payload: Partial<OfferDetailsData>; // Use Partial of OfferDetailsData for the payload
}

// Add interface for create offer response
export interface CreateOfferResponse {
  isSuccess: boolean;
  code: number;
  data: {
    offerId: number;
    navigateURL: string;
    title: string;
    productType: string;
    gameName: string;
    productName: string;
    screenShot: string;
    recentLable: string;
    recentURL: string;
    imageBlacklist: string;
  };
  message?: string;
}

// Interfaces for the image upload response
export interface UploadImageData {
  blobName: string;
  sasUri: string;
  created: string;
  length: number;
}

export interface UploadImageResponse {
  isSuccess: boolean;
  code: number;
  data: UploadImageData;
}

export interface UploadImageParams {
  file: File;
}

/**
 * Offers service for PlayerAuctions
 */
export const offersService = {
  /**
   * Get offers with pagination, sorting and filtering options
   *
   * @param pageIndex Current page index (default: 1)
   * @param pageSize Number of items per page (default: 10)
   * @param sortField Field to sort by (default: null)
   * @param sortOrder Sort direction (default: null)
   * @returns Promise with the offers data
   */
  getOffers: async ({
    pageIndex = 1,
    pageSize = 10,
    sortField = null,
    sortOrder = null,
  }: GetOffersParams = {}): Promise<OffersResponse> => {
    try {
      const url = new URL(`${API_CONFIG.baseUrl}/Offer/Offers`);

      // Add query parameters
      url.searchParams.append("pageIndex", pageIndex.toString());
      url.searchParams.append("pageSize", pageSize.toString());
      url.searchParams.append("sortField", sortField ?? "null");
      url.searchParams.append("sortOrder", sortOrder ?? "null");

      const response = await fetch(url.toString(), {
        method: "GET",
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      return (await response.json()) as OffersResponse;
    } catch (error) {
      console.error("Failed to fetch offers:", error);
      throw error;
    }
  },

  /**
   * Get details for a specific offer
   *
   * @param offerId The ID of the offer to fetch details for
   * @returns Promise with the offer details data
   */
  getOfferDetails: async ({
    offerId,
  }: GetOfferDetailsParams): Promise<OfferDetailsResponse> => {
    try {
      const url = new URL(`${API_CONFIG.baseUrl}/Offers/Account/${offerId}`);

      const response = await fetch(url.toString(), {
        method: "GET",
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      return (await response.json()) as OfferDetailsResponse;
    } catch (error) {
      console.error(`Failed to fetch details for offer ${offerId}:`, error);
      throw error;
    }
  },

  /**
   * Update details for a specific offer account
   *
   * @param offerId The ID of the offer to update
   * @param payload The data to update the offer with
   * @returns Promise with the updated offer details data
   */
  updateAccount: async ({
    offerId,
    payload,
  }: UpdateOfferParams): Promise<OfferDetailsResponse> => {
    try {
      const url = new URL(`${API_CONFIG.baseUrl}/offers/account`);

      const response = await fetch(url.toString(), {
        method: "PUT",
        headers: getAuthHeaders(),
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      return (await response.json()) as OfferDetailsResponse;
    } catch (error) {
      console.error(`Failed to update offer ${offerId}:`, error);
      throw error;
    }
  },

  /**
   * Create a new offer
   *
   * @param payload The data to create the offer with
   * @returns Promise with the created offer response
   */
  createAccount: async ({
    payload,
  }: CreateOfferParams): Promise<CreateOfferResponse> => {
    try {
      const url = new URL(`${API_CONFIG.baseUrl}/offers/account`);

      const response = await fetch(url.toString(), {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorBody = await response.text();
        console.error("API HTTP Error Response:", errorBody);
        throw new Error(
          `API error: ${response.status} - ${response.statusText}`
        );
      }

      const responseData = (await response.json()) as CreateOfferResponse;

      // Check for logical errors within the 200 OK response
      if (!responseData.isSuccess) {
        console.error("API Logical Error Response:", responseData);
        // Use the message from the API response if available, otherwise a generic error
        throw new Error(
          responseData.message ||
            `API indicated failure with code: ${responseData.code}`
        );
      }

      return responseData;
    } catch (error) {
      console.error(`Failed to create offer:`, error);
      // Re-throw the error so React Query can catch it
      throw error;
    }
  },
};

/**
 * Media service for PlayerAuctions
 */
export const mediaService = {
  /**
   * Upload an image to the server
   *
   * @param file The image file to upload
   * @returns Promise with the upload response data
   */
  uploadImage: async ({
    file,
  }: UploadImageParams): Promise<UploadImageResponse> => {
    try {
      const url = new URL(`${API_CONFIG.baseUrl}/media/images`);

      const formData = new FormData();
      formData.append("file", file);

      // Don't include Content-Type for FormData requests
      // but do include Authorization if available
      const headers: Record<string, string> = {};
      const token = authService.getToken();

      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await fetch(url.toString(), {
        method: "POST",
        headers,
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      return (await response.json()) as UploadImageResponse;
    } catch (error) {
      console.error("Failed to upload image:", error);
      throw error;
    }
  },
};

export default {
  offers: offersService,
  media: mediaService,
};
