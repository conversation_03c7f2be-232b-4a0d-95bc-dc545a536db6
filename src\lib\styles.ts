import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Modern styling utilities for shadcn/ui in 2025
 * For dark mode components with consistent design patterns
 */

/**
 * Combines class names with tailwind merge for efficient styling
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Modern component styling defaults for 2025 design language
 * Always using dark mode as the default
 */
export const modernDefaults = {
  // Modern button styles
  button: {
    default: "bg-primary text-primary-foreground hover:bg-primary/90",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/90",
    ghost: "hover:bg-accent hover:text-accent-foreground",
    destructive:
      "bg-destructive text-destructive-foreground hover:bg-destructive/90",
    outline: "border border-input hover:bg-accent hover:text-accent-foreground",
  },

  // Modern card styles
  card: "bg-card text-card-foreground border border-border rounded-lg shadow-sm",

  // Modern input styles
  input:
    "bg-background border-input text-foreground placeholder:text-muted-foreground",

  // Modern dialog styles
  dialog: "bg-background text-foreground",

  // Modern toast styles
  toast: "bg-background border-border text-foreground",

  // Elevation styles
  elevation: {
    sm: "shadow-sm",
    md: "shadow-md",
    lg: "shadow-lg",
  },

  // Modern motion preferences
  motion: {
    reduced: "motion-reduce:transition-none motion-reduce:transform-none",
    standard: "transition-all duration-200",
  },

  // Interactive states
  interactive: {
    hover: "hover:bg-accent hover:text-accent-foreground",
    focus:
      "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
    active: "active:scale-[0.98]",
  },
};

/**
 * Helper to build component variants with modern styling
 */
export function createVariants(
  base: string,
  variants: Record<string, Record<string, string>>
) {
  return {
    defaultVariants: Object.fromEntries(
      Object.keys(variants).map((key) => [key, Object.keys(variants[key])[0]])
    ),
    variants,
    base,
  };
}
