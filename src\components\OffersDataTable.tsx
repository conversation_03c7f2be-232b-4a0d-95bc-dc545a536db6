import React from "react";
import { useNavi<PERSON>, <PERSON> } from "react-router-dom";
import { Button } from "./ui/button";
import { Trash, Pencil } from "lucide-react";
import { offersService } from "@/lib/api";
import { useQuery } from "@tanstack/react-query";
import { parse, formatDistanceToNowStrict, formatRelative } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus } from "lucide-react";
import { toast } from "sonner";
import { useAtom } from "jotai";
import { draftOffersAtom, draftHelpers } from "@/lib/atoms";

export function OffersDataTable({ viewMode = "all" }: { viewMode?: "all" | "player-auctions" | "eldorado" }) {
  const navigate = useNavigate();
  const [draftOffers, setDraftOffers] = useAtom(draftOffersAtom);

  const { data, isLoading, isError, isFetching, error } = useQuery({
    queryKey: ["offers"],
    queryFn: async () => {
      // No need to cast here anymore, the type is inferred from offersService.getOffers
      const response = await offersService.getOffers({});
      return response;
    },
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });

  // Get drafts as an array
  const draftsArray = draftHelpers.getDraftsArray(draftOffers);

  const handleNavigateToDetails = (offerId: number) => {
    console.log(`Navigate to offer details for offer ${offerId}`);
    navigate(`/offers/${offerId}`);
  };

  const handleEditDraft = (draftId: string) => {
    console.log(`Edit draft ${draftId}`);
    // Navigate to create page with draft ID in query params
    navigate(`/offers/create?draftId=${draftId}`);
  };

  const handleDeleteDraft = (draftId: string) => {
    if (window.confirm("Are you sure you want to delete this draft?")) {
      setDraftOffers((prev) => draftHelpers.deleteDraft(prev, draftId));
      toast.success("Draft deleted");
    }
  };

  // Helper function to format expiration time
  const formatExpiration = (timeString: string): string => {
    try {
      // Define the format string based on the example "May-12-2025 08:20:22 AM"
      const format = "MMM-dd-yyyy hh:mm:ss aa";
      const expirationDate = parse(timeString, format, new Date());

      if (isNaN(expirationDate.getTime())) {
        // Handle invalid date format
        console.warn(`Invalid date format encountered: ${timeString}`);
        return `Expires ${timeString}`; // Fallback to original string
      }

      // Calculate distance strictly (e.g., "5 days", "3 hours")
      const distance = formatDistanceToNowStrict(expirationDate, {
        addSuffix: true,
      });

      // Replace "in" with "expires in"
      return distance.startsWith("in ") ? ` ${distance}` : ` ${distance} ago`;
    } catch (error) {
      console.error("Error parsing date:", error);
      return `Expires ${timeString}`; // Fallback on any error
    }
  };

  // Format date for drafts
  const formatDraftDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return formatRelative(date, new Date());
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };

  // Check if we have any drafts
  const hasDrafts = draftsArray.length > 0;

  return (
    <div className="space-y-4">
      {/* Drafts section - only show if we have drafts */}
      {hasDrafts && (
        <div className="border rounded-md mb-6">
          <div className="bg-muted/30 px-4 py-2 border-b flex items-center">
            <h3 className="text-sm font-medium">Drafts</h3>
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Price</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {draftsArray.map((draft) => (
                <TableRow key={draft.id}>
                  <TableCell
                    className="font-medium truncate max-w-xs cursor-pointer hover:underline"
                    onClick={() => handleEditDraft(draft.id)}
                  >
                    <div className="flex items-center gap-2">
                      {draft.title || "Untitled Draft"}
                    </div>
                  </TableCell>
                  <TableCell>{draft.price?.toFixed(2) || "N/A"}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7"
                        title="Edit draft"
                        onClick={() => handleEditDraft(draft.id)}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7 text-destructive hover:text-destructive"
                        title="Delete draft"
                        onClick={() => handleDeleteDraft(draft.id)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Loading state */}
      {(isLoading || isFetching) && (
        <div className="py-8 text-center text-muted-foreground">
          Loading offers...
        </div>
      )}

      {/* Error state */}
      {isError && (
        <div className="py-8 text-center text-destructive">
          Failed to load offers: {error?.message}
        </div>
      )}

      {/* Regular offers */}
      {data &&
        !isFetching &&
        (data.data.items.length === 0 && !hasDrafts ? (
          <Card className="bg-muted/40">
            <CardContent className="p-6 flex flex-col items-center justify-center">
              <p className="text-muted-foreground mb-4 text-center">
                No offers created yet
              </p>
              <Button asChild>
                <Link to="/offers/create">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Offer
                </Link>
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="border rounded-md">
            <div className="bg-muted/30 px-4 py-2 border-b flex items-center">
              <h3 className="text-sm font-medium">Published Offers</h3>
            </div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>ID</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Expires</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.data.items.map((offer) => (
                  <TableRow key={offer.offerId}>
                    <TableCell
                      className="font-medium truncate max-w-xs cursor-pointer hover:underline"
                      onClick={() => handleNavigateToDetails(offer.offerId)}
                    >
                      {offer.title}
                    </TableCell>
                    <TableCell>{offer.offerId}</TableCell>
                    <TableCell>{offer.totalPrice}</TableCell>
                    <TableCell>
                      {formatExpiration(offer.expiredTimeString)}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 text-destructive hover:text-destructive"
                          title="Delete offer"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ))}
    </div>
  );
}

export default OffersDataTable;
