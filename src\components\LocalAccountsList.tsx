import React, { useState, useEffect, useMemo } from "react";
import { localAccountsDb, LocalAccount } from "@/lib/db";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { AddLocalAccountDialog } from "@/components/AddLocalAccountDialog";
import { SelectOfferDialog } from "@/components/SelectOfferDialog";
import { EditLocalAccountDialog } from "@/components/EditLocalAccountDialog";
import { ImportAccountsDialog } from "@/components/ImportAccountsDialog";
import {
  Trash,
  Eye,
  EyeOff,
  Plus,
  Link,
  Copy,
  Check,
  SlidersHorizontal,
  Download,
  Calendar,
  Send,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Pop<PERSON>,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { offersService } from "@/lib/api";
import { useQuery } from "@tanstack/react-query";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Bar, BarChart, CartesianGrid, XAxis, YAxis } from "recharts";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import { useAtom } from 'jotai';
import { localAccountsTimelineFilterAtom, localAccountsStateFilterAtom, TimelineFilter, StateFilter } from '@/lib/atoms';

export function LocalAccountsList({ viewMode = "all" }: { viewMode?: "all" | "player-auctions" | "eldorado" }) {
  const [accounts, setAccounts] = useState<LocalAccount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showPasswordMap, setShowPasswordMap] = useState<{
    [id: number]: boolean;
  }>({});
  const [openStatusPopoverId, setOpenStatusPopoverId] = useState<number | null>(null);
  const [openTypePopoverId, setOpenTypePopoverId] = useState<number | null>(null);
  const [copiedAccountId, setCopiedAccountId] = useState<number | null>(null);

  // Filter states using Jotai atoms for persistence
  const [timelineFilter, setTimelineFilter] = useAtom(localAccountsTimelineFilterAtom);
  const [stateFilter, setStateFilter] = useAtom(localAccountsStateFilterAtom);

  // Fetch all offers using TanStack Query
  const { data: offersData, isLoading: isLoadingOffers } = useQuery({
    queryKey: ["offers"],
    queryFn: async () => {
      const response = await offersService.getOffers({});
      return response;
    },
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });

  const loadAccounts = async () => {
    setIsLoading(true);
    try {
      const accounts = await localAccountsDb.getAccounts();
      setAccounts(accounts);
      setShowPasswordMap({});
    } catch (error) {
      console.error("Error loading accounts:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadAccounts();
  }, []);

  const handleDelete = async (id: number) => {
    if (!id) return;
    if (window.confirm("Are you sure you want to delete this account?")) {
      try {
        await localAccountsDb.deleteAccount(id);
        setAccounts(accounts.filter((account) => account.id !== id));
      } catch (error) {
        console.error("Error deleting account:", error);
      }
    }
  };

  const handleStatusChange = async (id: number, status: string) => {
    try {
      const account = accounts.find((acc) => acc.id === id);
      if (!account) return;

      const updatedAccount = {
        ...account,
        status: status as "sold" | "listed" | "ready" | "none",
      };

      // Set date fields based on status
      if (status === "sold") {
        updatedAccount.soldDate = new Date();
      } else if (status === "listed") {
        updatedAccount.listedDate = new Date();
      }

      await localAccountsDb.updateAccount(updatedAccount);
      setAccounts(
        accounts.map((acc) => (acc.id === id ? updatedAccount : acc))
      );
      setOpenStatusPopoverId(null); // Close the popover after selection
    } catch (error) {
      console.error("Error updating account status:", error);
    }
  };

  const handleAccountTypeChange = async (id: number, accountType: string) => {
    try {
      const account = accounts.find((acc) => acc.id === id);
      if (!account) return;

      const updatedAccount = {
        ...account,
        accountType: accountType as "eldorado" | "player-auctions",
      };

      await localAccountsDb.updateAccount(updatedAccount);
      setAccounts(
        accounts.map((acc) => (acc.id === id ? updatedAccount : acc))
      );
      setOpenTypePopoverId(null); // Close the popover after selection
    } catch (error) {
      console.error("Error updating account type:", error);
    }
  };

  const handleCopyEmailPassword = async (account: LocalAccount) => {
    if (!account.id || !account.email || !account.password) return;
    try {
      await navigator.clipboard.writeText(
        `${account.email}:${account.password}`
      );
      setCopiedAccountId(account.id);
      setTimeout(() => setCopiedAccountId(null), 1500); // Reset after 1.5 seconds
    } catch (error) {
      console.error("Failed to copy:", error);
      // Optionally show an error message to the user
    }
  };

  const getMarkAsListedUrl = (email: string) => {
    return `http://goldfarm.szczawicznecrew.ovh/accounts/rebind?filterUsername=${encodeURIComponent(email)}&scriptTo=sale&amount=1`;
  };

  const togglePasswordVisibility = (id: number) => {
    setShowPasswordMap((prev) => ({ ...prev, [id]: !prev[id] }));
  };

  const getStatusBadge = (status: string | undefined) => {
    switch (status) {
      case "sold":
        return <Badge variant="destructive">Sold</Badge>;
      case "listed":
        return <Badge variant="secondary">Listed</Badge>;
      case "ready":
        return <Badge variant="default">Ready</Badge>;
      default:
        return <Badge variant="outline">None</Badge>;
    }
  };

  const getAccountTypeBadge = (accountType: string | undefined) => {
    switch (accountType) {
      case "eldorado":
        return <Badge variant="default" className="bg-orange-500 hover:bg-orange-600">Eldorado</Badge>;
      case "player-auctions":
        return <Badge variant="default" className="bg-blue-500 hover:bg-blue-600">Player Auctions</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  // Check if the offer exists in the list of offers
  const isOfferMissing = (offerId: string | undefined) => {
    if (!offerId || !offersData || !offersData.data || !offersData.data.items)
      return false;

    return !offersData.data.items.some(
      (offer) => offer.offerId.toString() === offerId
    );
  };

  // Export accounts to JSON file
  const handleExportAccounts = async () => {
    try {
      const allAccounts = await localAccountsDb.getAccounts();
      const json = JSON.stringify(allAccounts, null, 2);
      const blob = new Blob([json], { type: "application/json" });
      const url = URL.createObjectURL(blob);

      // Create a download link and trigger click
      const a = document.createElement("a");
      const date = new Date().toISOString().split("T")[0];
      a.href = url;
      a.download = `player-accounts-export-${date}.json`;
      document.body.appendChild(a);
      a.click();

      // Cleanup
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error exporting accounts:", error);
      alert("Failed to export accounts. Please try again.");
    }
  };

  // Helper function to filter accounts by timeline
  const filterByTimeline = (account: LocalAccount): boolean => {
    if (timelineFilter === "all") return true;

    // Get the relevant date based on account status
    let accountDate: Date | undefined;
    if (account.status === "sold" && account.soldDate) {
      accountDate = new Date(account.soldDate);
    } else if (account.status === "listed" && account.listedDate) {
      accountDate = new Date(account.listedDate);
    } else {
      // If no relevant date, include in all filters
      return true;
    }

    if (!accountDate) return true;

    const now = new Date();
    const timeDiff = now.getTime() - accountDate.getTime();
    const daysDiff = timeDiff / (1000 * 3600 * 24);

    switch (timelineFilter) {
      case "1month":
        return daysDiff <= 30;
      case "prevmonth":
        // Filter for previous calendar month
        const today = new Date();
        const prevMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const prevMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
        return accountDate >= prevMonth && accountDate <= prevMonthEnd;
      case "1week":
        return daysDiff <= 7;
      default:
        return true;
    }
  };

  // Helper function to filter accounts by state
  const filterByState = (account: LocalAccount): boolean => {
    switch (stateFilter) {
      case "sold":
        return account.status === "sold";
      case "all-except-sold":
        return account.status !== "sold";
      case "all":
      default:
        return true;
    }
  };

  // Helper function to get status priority for sorting
  const getStatusPriority = (status: string | undefined): number => {
    switch (status) {
      case "listed":
        return 1; // Highest priority
      case "ready":
        return 2;
      case "none":
        return 3;
      case "sold":
        return 4; // Lowest priority
      default:
        return 1; // Treat unknown status as high priority
    }
  };

  // Apply all filters and sort by status priority
  const filteredAccounts = accounts
    .filter(account => {
      // Filter by view mode first
      if (viewMode !== "all") {
        if (account.accountType !== viewMode) {
          return false;
        }
      }
      return filterByTimeline(account) && filterByState(account);
    })
    .sort((a, b) => {
      // Sort by status priority
      return getStatusPriority(a.status) - getStatusPriority(b.status);
    });

  if (isLoading) {
    return <div className="text-center py-4">Loading accounts...</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-3">
          <h2 className="text-xl font-semibold">Local Accounts</h2>
        </div>
        <div className="flex items-center gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 gap-1">
                <SlidersHorizontal className="h-3.5 w-3.5" />
                <span>Filters</span>
                {(() => {
                  // Count active filters
                  let activeFilters = 0;
                  if (timelineFilter !== "all") activeFilters++;
                  if (stateFilter !== "all") activeFilters++;

                  return activeFilters > 0 ? (
                    <Badge
                      variant="secondary"
                      className="ml-1 h-5 px-1.5 text-xs"
                    >
                      {activeFilters}
                    </Badge>
                  ) : null;
                })()}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-100 p-4">
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium leading-none mb-3 flex items-center gap-1.5">
                    <Calendar className="h-4 w-4" />
                    Timeline
                  </h4>
                  <Tabs
                    value={timelineFilter}
                    onValueChange={(value) => setTimelineFilter(value as TimelineFilter)}
                    className="w-full"
                  >
                    <TabsList className="grid grid-cols-4 w-full text-xs">
                      <TabsTrigger className="text-xs" value="all">All</TabsTrigger>
                      <TabsTrigger className="text-xs" value="1week">1 Week</TabsTrigger>
                      <TabsTrigger className="text-xs" value="1month">1 Month</TabsTrigger>
                      <TabsTrigger className="text-xs" value="prevmonth">Prev. month</TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>

                <Separator />

                <div className="space-y-3">
                  <h4 className="font-medium leading-none">State</h4>
                  <RadioGroup
                    value={stateFilter}
                    onValueChange={(value) => setStateFilter(value as StateFilter)}
                    className="flex flex-col space-y-1.5"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="all" id="all-states" />
                      <Label htmlFor="all-states">All</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="all-except-sold" id="all-except-sold" />
                      <Label htmlFor="all-except-sold">All Except Sold</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="sold" id="sold-only" />
                      <Label htmlFor="sold-only">Sold</Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
            </PopoverContent>
          </Popover>
          <div className="h-6 border-l mx-1"></div>
        
          <ImportAccountsDialog onImportComplete={loadAccounts} />
          <AddLocalAccountDialog onAccountAdded={loadAccounts} />
        </div>
      </div>
        {/* Sold Accounts Chart */}
      {accounts.length > 0 && (
        <div className="mt-8">
          <SoldAccountsChart accounts={accounts} viewMode={viewMode} timelineFilter={timelineFilter} />
        </div>
      )}
      {filteredAccounts.length === 0 ? (
        <Card className="bg-muted/40">
          <CardContent className="p-6 flex flex-col items-center justify-center">
            <p className="text-muted-foreground mb-4 text-center">
              {accounts.length === 0
                ? "No local accounts saved yet"
                : "No accounts to display with current filters"}
            </p>
            <AddLocalAccountDialog
              trigger={
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Your First Account
                </Button>
              }
              onAccountAdded={loadAccounts}
            />
          </CardContent>
        </Card>
      ) : (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>Offer ID</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Notes</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAccounts.map((account) => {
                const isPasswordVisible =
                  !!showPasswordMap[account.id as number];
                const isMissingOffer =
                  isOfferMissing(account.offerId) &&
                  account.status === "listed";
                return (
                  <TableRow key={account.id}>
                    <TableCell className="font-medium truncate max-w-xs">
                      <div className="relative group flex items-center">
                        <span className="truncate">{account.email}</span>
                        {account.id && account.password && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 absolute right-0 invisible group-hover:visible transition-opacity"
                            title="Copy email:password"
                            onClick={() => handleCopyEmailPassword(account)}
                          >
                            {copiedAccountId === account.id ? (
                              <Check className="h-4 w-4 text-green-500" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="space-y-1">
                      <div className="flex items-center gap-1.5">
                        {account.offerId || "-"}
                        {isMissingOffer && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <>
                                  <span className="text-amber-500 text-xs">
                                    Offer not found
                                  </span>
                                </>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>
                                  This offer ID cannot be found. The account may
                                  have been sold.
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                    </TableCell>

                    <TableCell>
                      <Popover
                        open={openTypePopoverId === account.id}
                        onOpenChange={(open) => {
                          setOpenTypePopoverId(
                            open ? (account.id as number) : null
                          );
                        }}
                      >
                        <PopoverTrigger asChild>
                          <div className="cursor-pointer hover:opacity-80">
                            {getAccountTypeBadge(account.accountType)}
                          </div>
                        </PopoverTrigger>
                        <PopoverContent className="w-60 p-0">
                          <div className="py-1.5 px-1">
                            <div className="grid grid-cols-1 gap-1">
                              <Button
                                variant="ghost"
                                className="justify-start text-xs h-8"
                                onClick={() =>
                                  handleAccountTypeChange(
                                    account.id as number,
                                    "player-auctions"
                                  )
                                }
                              >
                                <Badge variant="default" className="bg-blue-500 mr-2">
                                  Player Auctions
                                </Badge>
                                <span>Player Auctions account</span>
                              </Button>
                              <Button
                                variant="ghost"
                                className="justify-start text-xs h-8"
                                onClick={() =>
                                  handleAccountTypeChange(
                                    account.id as number,
                                    "eldorado"
                                  )
                                }
                              >
                                <Badge variant="default" className="bg-orange-500 mr-2">
                                  Eldorado
                                </Badge>
                                <span>Eldorado account</span>
                              </Button>
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </TableCell>

                    <TableCell>
                      <Popover
                        open={openStatusPopoverId === account.id}
                        onOpenChange={(open) => {
                          setOpenStatusPopoverId(
                            open ? (account.id as number) : null
                          );
                        }}
                      >
                        <PopoverTrigger asChild>
                          <div className="cursor-pointer hover:opacity-80">
                            {getStatusBadge(account.status)}
                          </div>
                        </PopoverTrigger>
                        <PopoverContent className="w-60 p-0">
                          <div className="py-1.5 px-1">
                            <div className="grid grid-cols-1 gap-1">
                              <Button
                                variant="ghost"
                                className="justify-start text-xs h-8"
                                onClick={() =>
                                  handleStatusChange(
                                    account.id as number,
                                    "listed"
                                  )
                                }
                              >
                                <Badge variant="secondary" className="mr-2">
                                  Listed
                                </Badge>
                                <span>Listed for sale</span>
                              </Button>
                              <Button
                                variant="ghost"
                                className="justify-start text-xs h-8"
                                onClick={() =>
                                  handleStatusChange(
                                    account.id as number,
                                    "ready"
                                  )
                                }
                              >
                                <Badge variant="default" className="mr-2">
                                  Ready
                                </Badge>
                                <span>Ready to use</span>
                              </Button>
                              <Button
                                variant="ghost"
                                className="justify-start text-xs h-8"
                                onClick={() =>
                                  handleStatusChange(
                                    account.id as number,
                                    "none"
                                  )
                                }
                              >
                                <Badge variant="outline" className="mr-2">
                                  None
                                </Badge>
                                <span>Not set</span>
                              </Button>
                              <Button
                                variant="ghost"
                                className="justify-start text-xs h-8"
                                onClick={() =>
                                  handleStatusChange(
                                    account.id as number,
                                    "sold"
                                  )
                                }
                              >
                                <Badge variant="destructive" className="mr-2">
                                  Sold
                                </Badge>
                                <span>Already sold</span>
                              </Button>
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </TableCell>

                    <TableCell>
                      {account.status === "sold" && account.soldDate
                        ? new Date(account.soldDate).toLocaleDateString()
                        : account.status === "listed" && account.listedDate
                        ? new Date(account.listedDate).toLocaleDateString()
                        : "-"}
                    </TableCell>

                    <TableCell>
                      {account.price ? `$${account.price.toFixed(2)}` : "-"}
                    </TableCell>

                    <TableCell className="truncate max-w-xs">
                      {account.notes || "-"}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-1">
                        {account.id && (
                          <SelectOfferDialog
                            accountId={account.id}
                            currentOfferId={account.offerId}
                            onOfferSelected={loadAccounts}
                            trigger={
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-7 w-7"
                                title="Bind to offer"
                              >
                                <Link className="h-4 w-4" />
                              </Button>
                            }
                          />
                        )}
                        {account.id && account.email && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-7 w-7"
                                  asChild
                                  title="Mark As Listed (on server)"
                                >
                                  <a
                                    href={getMarkAsListedUrl(account.email)}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                  >
                                    <Send className="h-4 w-4" />
                                  </a>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Mark As Listed (on server)</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                        {account.id && (
                          <EditLocalAccountDialog
                            account={account}
                            onAccountUpdated={loadAccounts}
                          />
                        )}

                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 text-destructive hover:text-destructive"
                          title="Delete account"
                          onClick={() => handleDelete(account.id as number)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      )}


    </div>
  );
}

// SoldAccountsChart component
function SoldAccountsChart({ accounts, viewMode = "all", timelineFilter }: { accounts: LocalAccount[]; viewMode?: "all" | "player-auctions" | "eldorado"; timelineFilter: TimelineFilter }) {
  // Helper function to get platform totals
  const getPlatformTotals = () => {
    const soldAccounts = accounts.filter(account => account.status === "sold");
    const paCount = soldAccounts.filter(account => account.accountType === "player-auctions").length;
    const eldoradoCount = soldAccounts.filter(account => account.accountType === "eldorado").length;
    return { paCount, eldoradoCount, total: soldAccounts.length };
  };

  // Helper function to filter accounts by timeline (for chart data only)
  const filterAccountsByTimeline = (account: LocalAccount): boolean => {
    if (timelineFilter === "all") return true;

    // Only consider sold accounts for the chart
    if (account.status !== "sold" || !account.soldDate) return false;

    const accountDate = new Date(account.soldDate);
    const now = new Date();
    const timeDiff = now.getTime() - accountDate.getTime();
    const daysDiff = timeDiff / (1000 * 3600 * 24);

    switch (timelineFilter) {
      case "1month":
        return daysDiff <= 30;
      case "prevmonth":
        // Filter for previous calendar month
        const today = new Date();
        const prevMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const prevMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
        return accountDate >= prevMonth && accountDate <= prevMonthEnd;
      case "1week":
        return daysDiff <= 7;
      default:
        return true;
    }
  };

  // Helper function to extract account type from notes
  const getAccountType = (notes: string): string => {
    // Extract base type from notes
    if (notes) {
      const parts = notes.split(',');
      return parts.length > 0 ? parts[0] : "unknown";
    }
    return "unknown";
  };

  // Group accounts by day and count sold accounts by account type
  const chartData = useMemo(() => {
    // Track unique account types and their colors
    const accountTypes = new Set<string>();
    const accountTypeToColor = new Map<string, string>();

    // Create a map to store counts by day
    const dailyData = new Map<string, {
      total: number;
      sold: number;
      accountTypes: Map<string, number>; // Map to store count by account type
    }>();

    // Determine date range based on timeline filter
    let daysToShow = 30; // Default to 30 days
    if (timelineFilter === "1week") {
      daysToShow = 7;
    } else if (timelineFilter === "prevmonth") {
      // For previous month, we'll calculate the exact days
      const today = new Date();
      const prevMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      const prevMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
      daysToShow = Math.ceil((prevMonthEnd.getTime() - prevMonth.getTime()) / (1000 * 3600 * 24));
    }

    // Get today's date and ensure it includes the full day
    const today = new Date();
    today.setHours(23, 59, 59, 999); // End of today

    // Calculate the start date
    const startDate = new Date(today);
    if (timelineFilter === "prevmonth") {
      startDate.setMonth(today.getMonth() - 1, 1);
      startDate.setHours(0, 0, 0, 0);
    } else {
      startDate.setDate(today.getDate() - daysToShow);
      startDate.setHours(0, 0, 0, 0);
    }

    // Generate day entries
    for (let i = 0; i <= daysToShow; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);
      // Format to YYYY-MM-DD
      const dayKey = currentDate.toISOString().split('T')[0];
      dailyData.set(dayKey, {
        total: 0,
        sold: 0,
        accountTypes: new Map<string, number>()
      });
    }

    // Make sure today is explicitly included (unless it's previous month filter)
    if (timelineFilter !== "prevmonth") {
      const todayKey = today.toISOString().split('T')[0];
      if (!dailyData.has(todayKey)) {
        dailyData.set(todayKey, {
          total: 0,
          sold: 0,
          accountTypes: new Map<string, number>()
        });
      }
    }

    // Count sold accounts by day and account type
    accounts
      .filter(account => account.status === "sold" && account.soldDate)
      .filter(filterAccountsByTimeline)
      .forEach(account => {
        const date = new Date(account.soldDate!);
        const dayKey = date.toISOString().split('T')[0];

        if (dailyData.has(dayKey)) {
          const data = dailyData.get(dayKey)!;
          data.total += 1;
          data.sold += 1;

          // Get base account type from notes
          const accountType = getAccountType(account.notes);
          accountTypes.add(accountType);

          // Assign color based on platform (last platform wins for mixed types)
          let color: string;
          switch (account.accountType) {
            case "eldorado":
              color = "#F97316"; // Orange
              break;
            case "player-auctions":
              color = "#3B82F6"; // Blue
              break;
            default:
              color = "#6B7280"; // Gray
              break;
          }
          accountTypeToColor.set(accountType, color);

          // Increment count for this account type
          const currentCount = data.accountTypes.get(accountType) || 0;
          data.accountTypes.set(accountType, currentCount + 1);
        }
      });

    // Convert map to array for Recharts
    return {
      data: Array.from(dailyData.entries())
        .sort((a, b) => a[0].localeCompare(b[0])) // Sort by date
        .map(([day, data]) => {
          // Create a base object with day and total
          const result: any = {
            day,
            total: data.total,
            sold: data.sold,
          };

          // Add each account type as a separate property
          data.accountTypes.forEach((count, type) => {
            result[type] = count;
          });

          return result;
        }),
      accountTypes: Array.from(accountTypes),
      accountTypeToColor
    };
  }, [accounts, timelineFilter]);

  // Generate dynamic chart configuration based on account types
  const chartConfig = useMemo(() => {
    const config: ChartConfig = {
      sold: {
        label: "All Sold Accounts",
        color: "hsl(var(--chart-1))",
      },
    };

    // Add each account type to the config with its assigned color
    chartData.accountTypes.forEach((type) => {
      const color = chartData.accountTypeToColor.get(type) || "#6B7280";

      config[type] = {
        label: type,
        color: color,
      };
    });

    return config;
  }, [chartData.accountTypes, chartData.accountTypeToColor]) satisfies ChartConfig;

  const platformTotals = getPlatformTotals();

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-base font-normal">
            Sold Accounts <span className="text-muted-foreground font-normal text-sm">({platformTotals.total} total: {platformTotals.paCount} PA, {platformTotals.eldoradoCount} Eldorado)</span>
          </CardTitle>
          <CardDescription>
            Daily count of sold accounts by type
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="aspect-auto h-[250px] w-full">
          <BarChart
            accessibilityLayer
            data={chartData.data}
            margin={{
              left: 12,
              right: 12,
              top: 12,
              bottom: 12,
            }}
            barGap={0}
            barCategoryGap={4}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="day"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.toLocaleDateString("en-US", {
                  month: "numeric",
                  day: "numeric",
                });
              }}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              allowDecimals={false}
              domain={[0, 'dataMax + 1']} // Ensure we have space at the top
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  className="w-[180px]"
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      weekday: "short",
                      month: "short",
                      day: "numeric",
                      year: "numeric",
                    });
                  }}
                />
              }
            />


            {/* Render a bar for each account type */}
            {chartData.accountTypes.map((type) => (
              <Bar
                key={type}
                dataKey={type}
                stackId="a"
                fill={chartConfig[type]?.color}
                name={type}
              />
            ))}
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
