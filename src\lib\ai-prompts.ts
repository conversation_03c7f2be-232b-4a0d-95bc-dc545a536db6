/**
 * AI Prompts for generating offer content
 */

export const titlePrompt = `
Generate a concise, appealing title for a game account offer based on the following input.
The title should be catchy, highlight key selling points, and be under 100 characters.
Do not use emojis or special characters.

MANDATORY RESTRICTION: Do not use the words "ultimate", "hardcore", or "ironman" in titles or descriptions unless these terms are specifically mentioned in the input. These terms have specific meanings in RuneScape (Ultimate Ironman accounts) and could mislead customers.

Example titles:
99 THIEVING Skiller | 3 Combat Level | Skiller - Your Key to Gielinor's Richest Pockets
99 HUNTER Skiller | 3 Combat Level | Skiller Account - Master of Traps, Not of Combat
99 FARMING | 3 Combat Skiller | 1000 Tithe Points - Your Green Thumb Guru
Gmaul Pure | 80 STR | 80 RNG | GOD BOOKS | QUESTS | GMauler Pure - Ready to Unleash KO Power!
Gmaul Pure | 70 STR | 70 RNG | God Books | Mithril Gloves - Your Next KO Machine!

If "Additional Information" is provided, use it to enhance your understanding of the account's features and selling points. This information may contain more detailed account stats, quest completions, or other important details that should be reflected in the title.

IMPORTANT: Return your response in the following JSON format:
{
  "title": "Your generated title here"
}
`;

// Eldorado-specific prompts for raw text generation
export const eldoradoTitlePrompt = `
Generate a concise, appealing title for a game account offer based on the following input.
The title should be catchy, highlight key selling points, and be under 100 characters.
Do not use emojis or special characters.

MANDATORY RESTRICTION: Do not use the words "ultimate", "hardcore", or "ironman" in titles or descriptions unless these terms are specifically mentioned in the input. These terms have specific meanings in RuneScape (Ultimate Ironman accounts) and could mislead customers.

Example titles:
99 THIEVING Skiller | 3 Combat Level | Skiller - Your Key to Gielinor's Richest Pockets
99 HUNTER Skiller | 3 Combat Level | Skiller Account - Master of Traps, Not of Combat
99 FARMING | 3 Combat Skiller | 1000 Tithe Points - Your Green Thumb Guru
Gmaul Pure | 80 STR | 80 RNG | GOD BOOKS | QUESTS | GMauler Pure - Ready to Unleash KO Power!
Gmaul Pure | 70 STR | 70 RNG | God Books | Mithril Gloves - Your Next KO Machine!

If "Additional Information" is provided, use it to enhance your understanding of the account's features and selling points. This information may contain more detailed account stats, quest completions, or other important details that should be reflected in the title.

IMPORTANT: Return your response in the following JSON format:
{
  "title": "Your generated title here"
}
`;

export const eldoradoDescriptionPrompt = `

[Format]
IMPORTANT: Return your response in the following JSON format:
{
  "description": "Your generated description here"
}
[/Format]

[Task]
- Create a detailed account offer description for Eldorado marketplace, Old School RuneScape, based on the provided example.
- Use plain text format only - no HTML, no special formatting, no markdown.

MANDATORY RESTRICTION: Do not use the words "ultimate", "hardcore", or "ironman" in titles or descriptions unless these terms are specifically mentioned in the input. These terms have specific meanings in RuneScape (Ultimate Ironman accounts) and could mislead customers.
- If a quest list is specified, display it clearly in a separate section.
- Strictly follow the raw text format for offer descriptions.
- Do not skip anything: rewrite every description and quest list for each account individually.
- Treat every account as separate and unique—do not copy/paste quest lists or reference previous ones.
- Use gamer-friendly but authentic language—avoid forced youth lingo.
- Only include quests if the list was provided; if not, omit the quest section entirely.
- If you're given with a quest list, then list them ALL in plain text format.
- Do not make false or unverified claims about what the account can/cannot do.
- Special notes (like bans) must appear in a separate, clearly marked section:
  - If the account has a ban history, show a clear warning note.
  - Explain that if you play fairly now, it's 100% safe.
  - Mention the reduced price due to the ban note.
- By default, always mention:
  - The account was never banned.
  - You are the original owner.
  - The account is delivered with an unregistered email using the legacy login system (unless specified otherwise).
- Focus on the 4 most important highlights for the account.
- Be sure to create an appealing title and description highlighting the key features of the account.
- Description must be comprehensive but engaging, highlighting the key features of the account.
- Strictly follow template of Example Response.
- Keep style of account overview as in example.

Accounts with 3 combat level do not mean that they are untouchable or that they have some super powers. They are just account that do not have combat stats trained, so theres nothing at all to brag about. This is just non trained combat account type.
Do not mess hunting account with term "wild". Wilderness is in game activity that is related to PK (player killing)

Use real OSRS terms in description and title. Do not create titles like we selling hunter account and title contains weird typing like "Master Tracker". Use real OSRS terms (but accurate)
[/Task]

[Example Account]
99 agility skiller, giant squirrel pet, full graceful, 3659 marks of grace, 3 combat
[/Example Account]

[Example Response]
99 Agility Skiller with Giant Squirrel Pet

Account Overview:
This is a premium 99 Agility account featuring the adorable Giant Squirrel pet and a nutty 3659 Marks of Grace.
With the full Graceful outfit already unlocked, you and your bushy-tailed buddy will be sprinting across Gielinor while others are still catching their breath.

Account Details:
- Sign-in method: Legacy Login System
- Email Status: Non-Registered Email
- Ban History: Never Banned
- Ownership: Original Owner

FAQ:

What is legacy login system?
The legacy login system refers to the traditional method of accessing RuneScape using an email address and password combination. This account uses this classic authentication approach, but you'll have the option to link it to your own Jagex account later if desired.

What does non-registered email mean?
A non-registered email means that no email address has been permanently bound to this account yet. This is a significant advantage for you as the new owner, as you'll be able to register your own personal email address to the account, establishing stronger ownership rights and enhanced account security.

[/Example Response]

`;

export const descriptionPrompt = `

[Format]
IMPORTANT: Return your response in the following JSON format:
{
  "description": "Your generated description here"
}
[/Format]

[Task]
- Create a similar account offer for PlayerAuctions, Old School RuneScape, based on the provided example.

MANDATORY RESTRICTION: Do not use the words "ultimate", "hardcore", or "ironman" in titles or descriptions unless these terms are specifically mentioned in the input. These terms have specific meanings in RuneScape (Ultimate Ironman accounts) and could mislead customers.
- If a quest list is specified, display it neatly in a new card/description section.
- Strictly follow the HTML of the example response/template for offer pages.
- Do not skip anything: rewrite every description and quest list for each account individually.
- Treat every account as separate and unique—do not copy/paste quest lists or reference previous ones.
- Use gamer-friendly but authentic language—avoid forced youth lingo.
- Do not change any icon or screenshot links; always keep them unchanged.
- Only include quests if the list was provided; if not, hide the quest box entirely.
If you're given with a quest list, then list them ALL and do not use graphics etc for bulletpoints.
- Do not make false or unverified claims about what the account can/cannot do.
- Special notes (like bans) must appear in a separate, clearly marked box:
  - If the account has a ban history, show a danger note.
  - Explain that if you play fairly now, it’s 100% safe.
  - Mention the reduced price due to the ban note.
- By default, always mention:
  - The account was never banned.
  - You are the original owner.
  - The account is delivered with an unregistered email using the legacy login system (unless specified otherwise).
- Limit status pills to the 4 most important highlights for the account.
- Never add or change FAQ items—do not modify the FAQ or footer.
- Be sure to stick to the exact title template—use highlights in the same style, and finish with a nice line suiting that account type.
- Description must be short but interesting, highlighting the key features of the account (use the correct color for highlighted words)

Accounts with 3 combat level do not mean that they are untouchable or that they have some super powers. They are just account that do not have combat stats trained, so theres nothing at all to brag about. This is just non trained combat account type.
Do not mess hunting account with term "wild". Wilderness is in game activity that is related to PK (player killing)

Use real OSRS terms in description and title. Do not create titles like we selling hunter account and title contains weird typing like "Master Tracker". Use real OSRS terms (but accurate)
[/Task]

[Example Account]
99 agility, giant squirrel, full graceful, 3659 marks of grace
[/Example Account]

[Example Response]
<div
  style="
    background-color: #141922;
    color: #f0f2f5;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI',
      sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    border-radius: 10px;
  "
>
  <div style="max-width: 800px; margin: 0 auto; padding: 3rem 1.5rem">
    <div style="text-align: center; margin-bottom: 3rem">
      <div
        class=""
        style="
          font-size: 3.5rem;
          font-weight: 800;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI',
            sans-serif;
          letter-spacing: -0.025em;
          margin: 0 0 1.5rem 0;
          color: transparent;
          background: linear-gradient(
            135deg,
            #6a61ff 0%,
            #9f40e0 50%,
            #ff3d8a 100%
          );
          -webkit-background-clip: text;
          background-clip: text;
          text-shadow: 0 2px 15px rgba(138, 92, 246, 0.3);
          position: relative;
          z-index: 1;
        "
      >
        Maxed Agility Account
      </div>
    </div>

    <!-- Account Showcase Section -->
    <div style="margin: 0 auto 3rem auto; max-width: 700px">
      <!-- Account Image -->
      <div
        style="
          background-color: #1b1e2d;
          border-radius: 0.75rem;
          position: relative;
          margin-bottom: 2rem;
          box-shadow: 0 20px 60px -30px rgba(0, 0, 0, 0.7);
        "
      >
        <div
          style="
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            color: #1b1e2d;
          "
        >
          <!-- Main Image KEEP IT ALWAYS THE SAME AND DO NOT CHANGE SINGLE CHARACTER -->
          <img
            id="mainOfferImg"
            alt="*****************.png"
            src="https://placehold.co/600x400"
            *="*****************.png"
            style="max-width: 100%; max-height: 100%; object-fit: contain"
          />
        </div>
      </div>

      <!-- Account Description -->
      <h2
        style="
          font-size: 1.25rem;
          font-weight: 600;
          margin: 0 0 2rem 0;
          color: #a4b2ff;
          text-align: left;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        "
      >
        Account Overview
      </h2>
      <div
        style="
          width: 100%;
          background-color: #1b1e2d;
          border-radius: 0.75rem;
          padding: 1.5rem 2rem;
          margin: 0;
          text-align: center;
          box-shadow: 0 10px 30px -15px rgba(0, 0, 0, 0.5);
        "
      >
        <p
          style="
            font-size: 1.05rem;
            color: #e0e5ed;
            line-height: 1.7;
            margin: 0;
          "
        >
          This is a premium
          <span style="color: #a4b2ff; font-weight: 600">99 Agility</span>
          account featuring the adorable
          <span style="color: #a4b2ff; font-weight: 600"
            >Giant Squirrel pet</span
          >
          and a nutty
          <span style="color: #a4b2ff; font-weight: 600"
            >3659 Marks of Grace</span
          >. With the
          <span style="color: #a4b2ff; font-weight: 600"
            >full Graceful outfit</span
          >
          already unlocked, you and your bushy-tailed buddy will be sprinting
          across Gielinor while others are still catching their breath.
        </p>
      </div>
    </div>

    <!-- Account Details -->
    <div style="margin: 0 auto 3.5rem auto; max-width: 700px">
      <h2
        style="
          font-size: 1.25rem;
          font-weight: 600;
          margin: 0 0 2rem 0;
          color: #a4b2ff;
          text-align: left;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        "
      >
        Account Details
      </h2>
      <div
        style="
          width: 100%;
          background-color: #1b1e2d;
          border-radius: 0.75rem;
          padding: 1.5rem 2rem;
          margin: 0;
        "
      >
        <div
          style="
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.75rem;
          "
        >
          <div>
            <div
              style="
                font-size: 0.75rem;
                color: #9ba3b1;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                margin-bottom: 0.3rem;
              "
            >
              Sign-in method
            </div>
            <div style="font-size: 1.05rem; color: #f5f7fa">
              Legacy Login System
            </div>
          </div>
          <div>
            <div
              style="
                font-size: 0.75rem;
                color: #9ba3b1;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                margin-bottom: 0.3rem;
              "
            >
              Email Status
            </div>
            <div style="font-size: 1.05rem; color: #f5f7fa">
              Non-Registered Email
            </div>
          </div>
          <div>
            <div
              style="
                font-size: 0.75rem;
                color: #9ba3b1;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                margin-bottom: 0.3rem;
              "
            >
              Ban History
            </div>
            <div style="font-size: 1.05rem; color: #f5f7fa">Never Banned</div>
          </div>
          <div>
            <div
              style="
                font-size: 0.75rem;
                color: #9ba3b1;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                margin-bottom: 0.3rem;
              "
            >
              Ownership
            </div>
            <div style="font-size: 1.05rem; color: #f5f7fa">Original Owner</div>
          </div>
        </div>
      </div>
    </div>

    <!-- FAQ Section -->
    <div style="margin: 0 auto 3rem auto; max-width: 700px">
      <h2
        style="
          font-size: 1.25rem;
          font-weight: 600;
          margin: 0 0 2rem 0;
          color: #a4b2ff;
          text-align: left;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        "
      >
        FAQ
      </h2>
      <div
        style="
          display: flex;
          flex-direction: column;
          gap: 2rem;
          width: 100%;
          margin: 0;
        "
      >
        <div>
          <h3
            style="
              font-size: 1.1rem;
              font-weight: 600;
              margin: 0 0 0.875rem 0;
              color: #a4b2ff;
            "
          >
            What is legacy login system?
          </h3>
          <div style="color: #c5ccd8; margin: 0; line-height: 1.7">
            The legacy login system refers to the traditional method of
            accessing RuneScape using an email address and password combination.
            This account uses this classic authentication approach, but you'll
            have the option to link it to your own Jagex account later if
            desired.
          </div>
        </div>
        <div>
          <h3
            style="
              font-size: 1.1rem;
              font-weight: 600;
              margin: 0 0 0.875rem 0;
              color: #a4b2ff;
            "
          >
            What does non-registered email mean?
          </h3>
          <p style="color: #c5ccd8; margin: 0; line-height: 1.7">
            A non-registered email means that no email address has been
            permanently bound to this account yet. This is a significant
            advantage for you as the new owner, as you'll be able to register
            your own personal email address to the account, establishing
            stronger ownership rights and enhanced account security.
          </p>
        </div>
      </div>
    </div>

    <footer
      style="
        max-width: 700px;
        margin: 0 auto;
        border-top: 1px solid #2a3445;
        padding-top: 2rem;
        text-align: center;
        color: #a5aebb;
        font-size: 0.875rem;
      "
    >
      <p>
        Looking for something specific? I'm here to help you find the perfect
        account.
      </p>
    </footer>
  </div>
</div>

[/Example Response]

`;
