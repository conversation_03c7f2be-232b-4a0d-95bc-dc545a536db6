import React from "react";
import OffersDataTable from "../components/OffersDataTable";
import { Plus } from "lucide-react";
import { Button } from "../components/ui/button";
import { ImageUploader } from "@/components/ImageUploader";
import { Link } from "react-router-dom";
import { LocalAccountsList } from "../components/LocalAccountsList";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

function MainPage() {
  const [viewMode, setViewMode] = React.useState<"all" | "player-auctions" | "eldorado">("all");

  return (
    <div className="min-h-screen bg-background">
      <main>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
          <div className="flex flex-col gap-8">
            {/* Header */}
            <header className="flex justify-between items-center">
              <div>
                <h1 className="text-4xl font-bold tracking-tight">
                  PlayerAuctions
                </h1>
                <p className="text-muted-foreground mt-1 max-w-2xl">
                  Manage your game account offers with our advanced marketplace
                  platform
                </p>
              </div>
            </header>

            {/* Mode Selector */}
            <div className="flex justify-center">
              <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as typeof viewMode)}>
                <TabsList className="grid w-[500px] grid-cols-3">
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="player-auctions">Player Auctions</TabsTrigger>
                  <TabsTrigger value="eldorado">Eldorado</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            {/* Offers section */}
            <section>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold">Your Offers</h2>
                <div className="flex gap-2">
                  {(viewMode === "all" || viewMode === "player-auctions") && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="gap-1 hover:bg-accent"
                      asChild
                    >
                      <Link to="/offers/create">
                        <Plus className="h-4 w-4" />
                        <span>New Offer</span>
                      </Link>
                    </Button>
                  )}
                  {(viewMode === "all" || viewMode === "eldorado") && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="gap-1 hover:bg-accent"
                      asChild
                    >
                      <Link to="/eldorado/offers/create">
                        <Plus className="h-4 w-4" />
                        <span>New Offer (Eldorado)</span>
                      </Link>
                    </Button>
                  )}
                </div>
              </div>
              <OffersDataTable viewMode={viewMode} />
            </section>

            {/* Local Accounts section */}
            <section className="mt-4 rounded-lg ">
              <LocalAccountsList viewMode={viewMode} />
            </section>
          </div>
        </div>
      </main>
    </div>
  );
}

export default MainPage;
