import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import forge from "node-forge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const accountSettingsPublicKey: string =
  "-----BEGIN PUBLIC KEY-----MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC0hnNIIDBLreG3t1lKWWZBxkZ+rhRUtRJDC1kj96I12HuBabjmQPXAZKwSTC5R+sK0zkQ7gKAvKisFvCwNg7bokdLRb/i/Et3GR6XTMBxZo1tLxiMp14SYDE0Q3+B/oKEFibXEiRwUiBywfDu/ps/Xmf5wQgoMiP1ErP1ik4MSBQIDAQAB-----E<PERSON> PUBLIC KEY-----";

export function encryptPassword(password: string) {
  try {
    // 1. Parse the RSA public key from PEM format
    const publicKey = forge.pki.publicKeyFromPem(accountSettingsPublicKey);

    // 2. Encrypt the password string using the public key.
    //    Forge's publicKey.encrypt defaults to RSAES-PKCS1-V1_5 padding
    //    The input 'password' is treated as a UTF-8 string by default here.
    const encryptedBytes = publicKey.encrypt(password); // Forge returns binary string

    // 3. Base64 encode the resulting binary encrypted data using btoa (as seen in original code)
    //    NOTE: btoa works on binary strings where char codes are 0-255.
    //    Forge's encrypt result should be compatible.
    //    For broader compatibility/Node.js, forge.util.encode64(encryptedBytes) is safer.
    if (typeof btoa === "undefined") {
      // Fallback for environments without btoa (like standard Node.js)
      return forge.util.encode64(encryptedBytes);
    } else {
      // Replicating the original code's use of btoa
      return btoa(encryptedBytes);
    }
  } catch (error) {
    console.error("Error encrypting password:", error);
  }
  return "";
}
