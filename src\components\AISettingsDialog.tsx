import React, { useState } from "react";
import { useAtom } from "jotai";
import { aiSettingsAtom } from "@/lib/atoms";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Settings, Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";

interface AISettingsDialogProps {
  children?: React.ReactNode;
}

export function AISettingsDialog({ children }: AISettingsDialogProps) {
  const [aiSettings, setAiSettings] = useAtom(aiSettingsAtom);
  const [isOpen, setIsOpen] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);
  const [tempSettings, setTempSettings] = useState(aiSettings);

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (open) {
      // Reset temp settings when opening
      setTempSettings(aiSettings);
    }
  };

  const handleSave = () => {
    // Validate API key
    if (!tempSettings.apiKey.trim()) {
      toast.error("API key is required");
      return;
    }

    // Validate models
    if (!tempSettings.titleModel || !tempSettings.descriptionModel) {
      toast.error("Both title and description models must be selected");
      return;
    }

    setAiSettings(tempSettings);
    setIsOpen(false);
    toast.success("AI settings saved successfully");
  };

  const handleCancel = () => {
    setTempSettings(aiSettings);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {children || (
          <Button variant="outline" size="sm" className="gap-2">
            <Settings className="h-4 w-4" />
            AI Settings
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>AI Settings</DialogTitle>
          <DialogDescription>
            Configure your AI models and API settings for content generation.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* API Key */}
          <div className="space-y-2">
            <Label htmlFor="apiKey">OpenRouter API Key</Label>
            <div className="relative">
              <Input
                id="apiKey"
                type={showApiKey ? "text" : "password"}
                value={tempSettings.apiKey}
                onChange={(e) =>
                  setTempSettings((prev) => ({
                    ...prev,
                    apiKey: e.target.value,
                  }))
                }
                placeholder="sk-or-v1-..."
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowApiKey(!showApiKey)}
              >
                {showApiKey ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {/* Title Model */}
          <div className="space-y-2">
            <Label htmlFor="titleModel">Title Generation Model</Label>
            <Input
              id="titleModel"
              value={tempSettings.titleModel}
              onChange={(e) =>
                setTempSettings((prev) => ({
                  ...prev,
                  titleModel: e.target.value,
                }))
              }
              placeholder="e.g., google/gemini-2.5-flash-preview-05-20"
            />
          </div>

          {/* Description Model */}
          <div className="space-y-2">
            <Label htmlFor="descriptionModel">Description Generation Model</Label>
            <Input
              id="descriptionModel"
              value={tempSettings.descriptionModel}
              onChange={(e) =>
                setTempSettings((prev) => ({
                  ...prev,
                  descriptionModel: e.target.value,
                }))
              }
              placeholder="e.g., google/gemini-2.5-flash-preview-05-20"
            />
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save Settings</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
