import { useState } from "react";
import {
  Di<PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from "./ui/dialog";
import { FileSystemExplorer } from "./FileSystemExplorer";

interface FileSystemExplorerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onFilesSelected?: (files: File[]) => void;
  accept?: Record<string, string[]>;
  multiple?: boolean;
  defaultPath?: string;
  autoNavigateToFolder?: string;
  title?: string;
}

export function FileSystemExplorerDialog({
  open,
  onOpenChange,
  onFilesSelected,
  accept,
  multiple = false,
  defaultPath = "C:\\Users\\<USER>\\Desktop\\GoldfarmData\\accs_sale\\accounts",
  autoNavigateToFolder,
  title = "Select Files"
}: FileSystemExplorerDialogProps) {
  const handleFilesSelected = (files: File[]) => {
    onFilesSelected?.(files);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] p-0">
        <DialogHeader className="px-6 py-4">
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <div className="px-6 pb-6">
          <FileSystemExplorer
            onFilesSelected={handleFilesSelected}
            onCancel={handleCancel}
            accept={accept}
            multiple={multiple}
            defaultPath={defaultPath}
            autoNavigateToFolder={autoNavigateToFolder}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}