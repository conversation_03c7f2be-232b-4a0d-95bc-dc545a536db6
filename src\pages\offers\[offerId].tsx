import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { offersService } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import Editor, { ContentEditableEvent } from "react-simple-wysiwyg";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { UploadCloud } from "lucide-react";
import { ImageUploader } from "@/components/ImageUploader";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowL<PERSON>t, Eye, EyeOff, Save } from "lucide-react";

import type {
  OfferDetailsData,
  UpdateOfferParams,
  AutoDeliveryInfo,
} from "@/lib/api";
import { encryptPassword } from "@/lib/utils";

export default function OfferDetailPage() {
  const { offerId } = useParams<{ offerId: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [showPassword, setShowPassword] = useState(false);

  // Fetch offer details
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["offer", offerId],
    queryFn: async () => {
      if (!offerId || isNaN(parseInt(offerId))) {
        throw new Error("Invalid offer ID");
      }
      return await offersService.getOfferDetails({
        offerId: parseInt(offerId),
      });
    },
    enabled: !!offerId,
  });

  // State for edited data, explicitly typed
  const [editedData, setEditedData] = useState<Partial<OfferDetailsData>>({});
  // Add state for retypePassword
  const [retypePassword, setRetypePassword] = useState<string>("");

  // Effect to initialize editedData when data loads or changes
  useEffect(() => {
    if (data?.data) {
      setEditedData(data.data);
      // If there's a password, initialize retypePassword with it
      if (data.data.autoDelivery?.password) {
        setRetypePassword(data.data.autoDelivery.password);
      }
    }
  }, [data]);

  // Handle input changes, handling nested autoDelivery fields
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    field: string
  ) => {
    const { value } = e.target;
    setEditedData((prev: Partial<OfferDetailsData>) => {
      const keys = field.split(".");
      if (keys.length > 1 && keys[0] === "autoDelivery") {
        const subField = keys[1];
        // Create a new partial autoDelivery object safely
        const updatedAutoDelivery: Partial<AutoDeliveryInfo> = {
          ...(prev.autoDelivery || {}),
          [subField]: value,
        };
        return {
          ...prev,
          autoDelivery: updatedAutoDelivery,
        };
      }
      // Ensure numeric fields are stored as numbers if needed by API
      const isNumericField = [
        "offerDuration",
        "freeInsurance",
        "price",
      ].includes(field);
      const parsedValue = isNumericField ? parseFloat(value) || 0 : value;
      return { ...prev, [field]: parsedValue };
    });
  };

  // Handle description changes specifically - fixed type
  const handleDescriptionChange = (e: ContentEditableEvent) => {
    setEditedData((prev: Partial<OfferDetailsData>) => ({
      ...prev,
      offerDesc: e.target.value,
    }));
  };

  // Method to update main offer image in description
  const updateMainOfferImage = (imageUrl: string) => {
    // Get the current description HTML
    const descriptionHtml = editedData?.offerDesc || "";

    // Check if the image with id "mainOfferImg" exists in the description
    if (descriptionHtml.includes('id="mainOfferImg"')) {
      // Create a safer approach to update the image
      let updatedHtml = descriptionHtml;

      try {
        // First approach: Try to handle malformed HTML with regex
        // This searches for an img tag with id="mainOfferImg" and replaces its src
        updatedHtml = descriptionHtml.replace(
          /<img[^>]*id=["']mainOfferImg["'][^>]*(?:src=["'][^"']*["'])?[^>]*>/i,
          `<img id="mainOfferImg" src="${imageUrl}" alt="Main offer image" style="max-width: 100%; height: auto; margin: 10px 0;">`
        );

        // If no replacement happened, try a more aggressive approach
        if (updatedHtml === descriptionHtml) {
          console.log("First replacement approach failed, trying alternative");
          // Find the position of the mainOfferImg id
          const idPosition = descriptionHtml.indexOf('id="mainOfferImg"');
          if (idPosition > 0) {
            // Find the img tag start
            const tagStart = descriptionHtml.lastIndexOf("<img", idPosition);
            // Find the tag end (allowing for malformed tags)
            let tagEnd = descriptionHtml.indexOf(">", idPosition);
            if (tagEnd === -1) tagEnd = descriptionHtml.length;

            // Extract the entire img tag
            const originalImgTag = descriptionHtml.substring(
              tagStart,
              tagEnd + 1
            );
            console.log("Found original tag:", originalImgTag);

            // Replace with well-formed tag
            const newImgTag = `<img id="mainOfferImg" src="${imageUrl}" alt="Main offer image" style="max-width: 100%; height: auto; margin: 10px 0;">`;
            updatedHtml = descriptionHtml.replace(originalImgTag, newImgTag);
          }
        }
      } catch (err) {
        console.error("Error updating image in HTML:", err);
        // Fallback: append the image
        const imgTag = `<img id="mainOfferImg" src="${imageUrl}" alt="Main offer image" style="max-width: 100%; height: auto; margin: 10px 0;" />`;
        updatedHtml = `${descriptionHtml}\n${imgTag}`;
      }

      // Update the description in the state
      setEditedData((prev) => ({
        ...prev,
        offerDesc: updatedHtml,
      }));

      toast.success("Main image updated successfully");
    } else {
      // If no image with the ID exists, append one to the description
      const imgTag = `<img id="mainOfferImg" src="${imageUrl}" alt="Main offer image" style="max-width: 100%; height: auto; margin: 10px 0;" />`;
      const updatedHtml = `${descriptionHtml}\n${imgTag}`;

      setEditedData((prev) => ({
        ...prev,
        offerDesc: updatedHtml,
      }));

      toast.success("Main image added to description");
    }
  };

  // Handle back button
  const handleBack = () => {
    navigate("/");
  };

  // Mutation for updating the offer
  const updateMutation = useMutation({
    mutationFn: (params: UpdateOfferParams) => {
      if (!offerId) throw new Error("Offer ID is missing");
      console.log("🚀 ~ mutationFn: Updating offer with params:", params);
      return offersService.updateAccount(params);
    },
    onSuccess: () => {
      console.log("✅ ~ onSuccess: Offer updated successfully!");
      toast.success("Offer updated successfully!");
      // Invalidate all offers queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["offers"] });
      navigate("/");
    },
    onError: (err) => {
      console.error("❌ ~ onError: Failed to update offer:", err);
      toast.error(
        `Failed to update offer: ${
          err instanceof Error ? err.message : "Unknown error"
        }`
      );
    },
  });

  // Handle save button click
  const handleSave = () => {
    // Construct the payload. Ensure required fields are present or handled.
    console.log("💾 ~ handleSave: Attempting to save...");
    console.log("💾 ~ handleSave: Current editedData:", editedData);
    console.log("💾 ~ handleSave: Original data.data:", data?.data);

    if (!editedData || !offerId) {
      console.warn("💾 ~ handleSave: Save cancelled - data missing.");
      toast.error("Cannot save, essential data is missing.");
      return;
    }

    // Merge original data with edits to ensure all fields are present
    // This assumes `data.data` holds the original fetched data structure
    const payload = {
      ...(data?.data || {}),
      ...editedData,
      screenShot:
        "https://image-cdn-p.azureedge.net/offer-image/mop1213/20250412160541541.png",
    } as OfferDetailsData;

    // Encrypt passwords before sending
    if (payload.autoDelivery?.password) {
      payload.autoDelivery = {
        ...payload.autoDelivery,
        password: encryptPassword(payload.autoDelivery.password),
        retypePassword: encryptPassword(retypePassword),
        original: payload.autoDelivery.original,
        current: payload.autoDelivery.current,
        isInfoSame: true,
        choose5: true,
      };
    }

    // Add more robust validation as needed
    if (!payload.title?.trim()) {
      console.warn("💾 ~ handleSave: Save cancelled - title empty.");
      toast.error("Offer title cannot be empty.");
      return;
    }
    if (payload.price === undefined || payload.price < 0) {
      console.warn("💾 ~ handleSave: Save cancelled - invalid price.");
      toast.error("Price must be a non-negative number.");
      return;
    }
    if (payload.offerDuration === undefined || payload.offerDuration <= 0) {
      console.warn("💾 ~ handleSave: Save cancelled - invalid duration.");
      toast.error("Duration must be a positive number.");
      return;
    }

    payload.isAuto = true;
    payload.actionType = "";
    payload.agreeCheck = true;

    console.log("💾 ~ handleSave: Calling mutation with payload:", payload);
    updateMutation.mutate({ offerId: parseInt(offerId), payload });
  };

  // Get the active data for display (use editedData directly as it's initialized)
  const activeData = editedData;

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-5xl mx-auto">
          <div className="flex items-center mb-8">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleBack}
              className="mr-4"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <Skeleton className="h-8 w-64" />
          </div>

          <Card>
            <CardHeader>
              <Skeleton className="h-8 w-48 mb-2" />
              <Skeleton className="h-4 w-full max-w-md" />
            </CardHeader>
            <CardContent className="space-y-8">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-10 w-full" />
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-5xl mx-auto">
          <Card className="border-destructive">
            <CardHeader>
              <CardTitle className="text-destructive">
                Error Loading Offer
              </CardTitle>
              <CardDescription>
                {error instanceof Error
                  ? error.message
                  : "Failed to load offer details"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={handleBack} variant="outline">
                Go Back
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Check if data exists before rendering the form
  if (!data?.data) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-5xl mx-auto text-center">
          <p>Offer data not found.</p>
          <Button onClick={handleBack} variant="outline" className="mt-4">
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-5xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleBack}
              className="mr-4"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-xl pe-3 font-bold tracking-tight">
                {activeData?.title ?? "Offer Details"}
              </h1>
              <p className="text-muted-foreground text-xs">ID: {offerId}</p>
            </div>
          </div>
          {/* Save Button in Header */}
          <Button
            onClick={handleSave}
            disabled={updateMutation.isPending}
            size="lg" // Larger button
            className="rounded-full" // Rounded style
          >
            {updateMutation.isPending ? (
              "Saving..."
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" /> Save Changes
              </>
            )}
          </Button>
        </div>

        <div className="space-y-8">
          {/* Basic Information Card */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                General information about your offer
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={activeData?.title || ""}
                  onChange={(e) => handleChange(e, "title")}
                  className="h-12"
                />
              </div>

              {/* Description Editor/Textarea */}
              <div className="space-y-2 ">
                <div className="flex items-center justify-between mb-2">
                  <Label htmlFor="offerDesc">Description</Label>
                  <div className="flex items-center space-x-2"></div>
                </div>
                <div className="flex-grow overflow-y-auto border rounded-md">
                  <Editor
                    value={activeData?.offerDesc || ""}
                    onChange={handleDescriptionChange} // Correct type now matches
                    containerProps={{
                      style: {
                        height: "100%",
                        minHeight: "500px",
                        resize: "vertical",
                        border: "none",
                      },
                    }} // Added styling for editor
                    className="bg-background h-[600px] overflow-y-auto" // Added padding and background
                  />
                </div>
              </div>

              {/* Image Upload */}
              <div className="space-y-2">
                <Label htmlFor="imageUpload">
                  Offer's Main Image (description)
                </Label>
                <div className="flex items-center gap-4">
                  <ImageUploader
                    className="h-50 w-100 min-h-0 p-3"
                    onUploadSuccess={(data) => {
                      const imageUrl = data.sasUri;
                      console.log("🚀 ~ onUploadSuccess: ", imageUrl);
                      updateMainOfferImage(imageUrl);
                    }}
                    onUploadError={(error) => {
                      toast.error(`Upload failed: ${error.message}`);
                    }}
                  />
                </div>
              </div>

              {/* Duration and Insurance Inputs */}
              <div className="grid gap-6 md:grid-cols-2">
                {" "}
                {/* Increased gap */}
                <div className="space-y-2">
                  <Label htmlFor="offerDuration">Duration (days)</Label>
                  <Input
                    id="offerDuration"
                    type="number"
                    value={activeData?.offerDuration || 0}
                    onChange={(e) => handleChange(e, "offerDuration")}
                    className="h-12"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="freeInsurance">Free Insurance</Label>
                  <Input
                    id="freeInsurance"
                    type="number"
                    value={activeData?.freeInsurance || 0}
                    onChange={(e) => handleChange(e, "freeInsurance")}
                    className="h-12"
                  />
                </div>
              </div>

              {/* Price Input */}
              <div className="space-y-2">
                <Label htmlFor="price">Price ($)</Label>
                <Input
                  id="price"
                  type="number"
                  step="0.01"
                  value={activeData?.price || 0}
                  onChange={(e) => handleChange(e, "price")}
                  className="h-12"
                />
              </div>
            </CardContent>
          </Card>

          {/* Auto Delivery Information Card */}
          <Card>
            <CardHeader>
              <CardTitle>Auto Delivery Information</CardTitle>
              <CardDescription>
                Information required for automated delivery (if applicable)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Login Name and Character Name */}
              <div className="grid gap-6 md:grid-cols-2">
                {" "}
                {/* Increased gap */}
                <div className="space-y-2">
                  <Label htmlFor="loginName">Login Name</Label>
                  <Input
                    id="loginName"
                    value={activeData?.autoDelivery?.loginName || ""}
                    onChange={(e) => handleChange(e, "autoDelivery.loginName")}
                    className="h-12"
                    autoComplete="username" // Added autocomplete hint
                  />
                </div>
              </div>

              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="characterName">Character Name</Label>
                  <Input
                    id="characterName"
                    value={activeData?.autoDelivery?.characterName || ""}
                    onChange={(e) =>
                      handleChange(e, "autoDelivery.characterName")
                    }
                    className="h-12"
                  />
                </div>
              </div>

              {/* Password Input */}
              <div className="grid gap-6 md:grid-cols-2">
                {" "}
                {/* Increased gap */}
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={activeData?.autoDelivery?.password || ""}
                      onChange={(e) => handleChange(e, "autoDelivery.password")}
                      className="h-12 pr-10" // Ensure padding for icon
                      autoComplete="new-password" // Added autocomplete hint
                    />
                    <Button
                      type="button" // Important for not submitting forms
                      variant="ghost"
                      size="icon"
                      className="absolute right-1 top-1/2 transform -translate-y-1/2 h-10 w-10 text-muted-foreground hover:text-foreground" // Adjusted styling
                      onClick={() => setShowPassword(!showPassword)}
                      aria-label={
                        showPassword ? "Hide password" : "Show password"
                      } // Accessibility
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5" /> // Slightly larger icons
                      ) : (
                        <Eye className="h-5 w-5" />
                      )}
                    </Button>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Delivery Instructions */}
              <div className="space-y-2">
                <Label htmlFor="deliveryInstructions">
                  Delivery Instructions
                </Label>
                <Textarea
                  id="deliveryInstructions"
                  value={activeData?.autoDelivery?.instruction || ""}
                  onChange={(e) => handleChange(e, "autoDelivery.instruction")}
                  rows={5} // Increased rows slightly
                  className="resize-y" // Allow vertical resize
                  placeholder="Provide clear steps for delivery..."
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
