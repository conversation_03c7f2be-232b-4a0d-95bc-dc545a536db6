
// const DEFAULT_MODEL = "google/gemini-2.5-pro-preview";
const DEFAULT_MODEL = "google/gemini-2.5-flash-preview-05-20";

// Types for OpenRouter API
interface OpenRouterMessage {
  role: "user" | "assistant" | "system";
  content: string;
}

interface OpenRouterRequest {
  model: string;
  messages: OpenRouterMessage[];
}

interface OpenRouterResponse {
  id: string;
  choices: {
    message: {
      content: string;
      role: string;
    };
    finish_reason: string;
    index: number;
  }[];
  model: string;
  created: number;
}

/**
 * Helper function to clean up JSON responses
 * This handles cases where the model might add markdown code blocks or other formatting
 */
function cleanJsonResponse(response: string): string {
  // Remove markdown code blocks if present
  let cleaned = response.trim();

  // Check for JSON code blocks (```json ... ```)
  const jsonBlockRegex = /```(?:json)?\s*([\s\S]*?)\s*```/;
  const jsonBlockMatch = cleaned.match(jsonBlockRegex);
  if (jsonBlockMatch && jsonBlockMatch[1]) {
    cleaned = jsonBlockMatch[1].trim();
  }

  // Check if the response starts with a backtick (incomplete code block)
  if (cleaned.startsWith('`') && !cleaned.startsWith('```')) {
    cleaned = cleaned.substring(1).trim();
  }

  // Check if the response ends with a backtick (incomplete code block)
  if (cleaned.endsWith('`') && !cleaned.endsWith('```')) {
    cleaned = cleaned.substring(0, cleaned.length - 1).trim();
  }

  return cleaned;
}

/**
 * OpenRouter API service
 */
export const openRouterService = {
  /**
   * Generate text using OpenRouter API
   *
   * @param prompt The prompt to send to the API
   * @param input The user input to include in the prompt
   * @param apiKey The OpenRouter API key
   * @param additionalInfo Optional additional information from a text file
   * @param model Optional model to use (defaults to Gemini 2.5 Flash)
   * @returns Promise with the generated text
   */
  generateText: async ({
    prompt,
    input,
    apiKey,
    additionalInfo = null,
    model = DEFAULT_MODEL,
  }: {
    prompt: string;
    input: string;
    apiKey: string;
    additionalInfo?: string | null;
    model?: string;
  }): Promise<string> => {
    try {
      // Construct the content with or without additional info
      let content = `${prompt}\n\nInput: ${input}`;

      // Add additional info if provided
      if (additionalInfo) {
        content += `\n\nAdditional Information:\n${additionalInfo}`;
      }

      const messages: OpenRouterMessage[] = [
        {
          role: "user",
          content,
        },
      ];

      const requestBody: OpenRouterRequest = {
        model,
        messages,
      };

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiKey}`,
          "HTTP-Referer": window.location.origin,
          "X-Title": "PlayerAuctions Client",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
      }

      const data = (await response.json()) as OpenRouterResponse;

      if (!data.choices || data.choices.length === 0) {
        throw new Error("No response from OpenRouter API");
      }

      // Get the raw content from the API response
      const rawContent = data.choices[0].message.content;

      // Clean up the response to handle potential JSON formatting issues
      return cleanJsonResponse(rawContent);
    } catch (error) {
      console.error("Failed to generate text with OpenRouter:", error);
      throw error;
    }
  },
};
