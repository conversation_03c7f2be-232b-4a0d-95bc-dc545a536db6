export interface LocalAccount {
  id?: number;
  email: string;
  password: string;
  notes: string;
  offerId?: string;
  status?: "sold" | "listed" | "ready" | "none";
  accountType?: "eldorado" | "player-auctions";
  soldDate?: Date;
  listedDate?: Date;
  price?: number;
}

class LocalAccountsDatabase {
  private db: IDBDatabase | null = null;
  private readonly DB_NAME = "local-accounts-db";
  private readonly DB_VERSION = 4;
  private readonly STORE_NAME = "accounts";

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.DB_NAME, this.DB_VERSION);

      request.onerror = () => {
        console.error("Database error:", request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = request.result;
        if (!db.objectStoreNames.contains(this.STORE_NAME)) {
          db.createObjectStore(this.STORE_NAME, {
            keyPath: "id",
            autoIncrement: true,
          });
        }

        if (event.oldVersion < 2) {
          const store = request.transaction?.objectStore(this.STORE_NAME);
          if (store && !store.indexNames.contains("status")) {
            this.getAccounts().then((accounts) => {
              accounts.forEach((account) => {
                if (!account.status) {
                  account.status = "none";
                  store.put(account);
                }
              });
            });
          }
        }

        if (event.oldVersion < 4) {
          const store = request.transaction?.objectStore(this.STORE_NAME);
          if (store) {
            this.getAccounts().then((accounts) => {
              accounts.forEach((account) => {
                if (!account.accountType) {
                  // Default to player-auctions if not set
                  account.accountType = "player-auctions";
                  store.put(account);
                }
              });
            });
          }
        }
      };
    });
  }

  async addAccount(account: Omit<LocalAccount, "id">): Promise<number> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(this.STORE_NAME, "readwrite");
      const store = transaction.objectStore(this.STORE_NAME);
      const request = store.add(account);

      request.onsuccess = () => {
        resolve(request.result as number);
      };

      request.onerror = () => {
        console.error("Error adding account:", request.error);
        reject(request.error);
      };
    });
  }

  async getAccounts(): Promise<LocalAccount[]> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(this.STORE_NAME, "readonly");
      const store = transaction.objectStore(this.STORE_NAME);
      const request = store.getAll();

      request.onsuccess = () => {
        const accounts = request.result as LocalAccount[];
        // Ensure all accounts have proper default values
        const normalizedAccounts = accounts.map(account => ({
          ...account,
          status: account.status || "none",
          accountType: account.accountType || "player-auctions"
        }));
        resolve(normalizedAccounts);
      };

      request.onerror = () => {
        console.error("Error getting accounts:", request.error);
        reject(request.error);
      };
    });
  }

  async updateAccount(account: LocalAccount): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(this.STORE_NAME, "readwrite");
      const store = transaction.objectStore(this.STORE_NAME);
      const request = store.put(account);

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = () => {
        console.error("Error updating account:", request.error);
        reject(request.error);
      };
    });
  }

  async deleteAccount(id: number): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(this.STORE_NAME, "readwrite");
      const store = transaction.objectStore(this.STORE_NAME);
      const request = store.delete(id);

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = () => {
        console.error("Error deleting account:", request.error);
        reject(request.error);
      };
    });
  }

  async exportAccounts(): Promise<string> {
    const accounts = await this.getAccounts();
    return JSON.stringify(accounts, null, 2);
  }

  async importAccounts(jsonData: string): Promise<void> {
    if (!this.db) await this.init();

    try {
      const accounts = JSON.parse(jsonData) as LocalAccount[];
      
      return new Promise((resolve, reject) => {
        const transaction = this.db!.transaction(this.STORE_NAME, "readwrite");
        const store = transaction.objectStore(this.STORE_NAME);
        
        // Clear existing data first
        const clearRequest = store.clear();
        
        clearRequest.onsuccess = () => {
          let completed = 0;
          const total = accounts.length;
          
          if (total === 0) {
            resolve();
            return;
          }
          
          accounts.forEach((account) => {
            // Normalize the account data
            const normalizedAccount = {
              ...account,
              status: account.status || "none",
              accountType: account.accountType || "player-auctions"
            };
            
            // Remove id to let auto-increment handle it
            delete normalizedAccount.id;
            
            const addRequest = store.add(normalizedAccount);
            
            addRequest.onsuccess = () => {
              completed++;
              if (completed === total) {
                resolve();
              }
            };
            
            addRequest.onerror = () => {
              console.error("Error importing account:", addRequest.error);
              reject(addRequest.error);
            };
          });
        };
        
        clearRequest.onerror = () => {
          console.error("Error clearing database:", clearRequest.error);
          reject(clearRequest.error);
        };
      });
    } catch (error) {
      throw new Error("Invalid JSON data");
    }
  }

  async clearAllAccounts(): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(this.STORE_NAME, "readwrite");
      const store = transaction.objectStore(this.STORE_NAME);
      const request = store.clear();

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = () => {
        console.error("Error clearing accounts:", request.error);
        reject(request.error);
      };
    });
  }
}

export const localAccountsDb = new LocalAccountsDatabase();
